public with sharing class MenuStrumentiController {

    public class Tabs {
        @AuraEnabled 
        public String label;
        @AuraEnabled 
        public String name;
        @AuraEnabled 
        public String context;
        @AuraEnabled 
        public String userContext;
    }

    @AuraEnabled(cacheable=true)
    public static List<Tabs> getTabs() {
        Map<String, Menu_Strumenti_Tabs__mdt> menuStrumentiTabsMap = Menu_Strumenti_Tabs__mdt.getAll();

        Set<String> mandatiAbilitati = new Set<String>();
        for(PermissionSetAssignment permissionSetAss : [SELECT Id, PermissionSet.Name, PermissionSetGroupId, AssigneeId, Assignee.FederationIdentifier 
                                                        FROM PermissionSetAssignment 
                                                        WHERE PermissionSet.Name IN ('MandatoUnipolSai', 'MandatoUnipolRental', 'MandatoUnipolTech', 'MandatoUniSalute') AND AssigneeId =: UserInfo.getUserId()]) {

            mandatiAbilitati.add(permissionSetAss.PermissionSet.Name);
        }

        User usr = [SELECT Id, FederationIdentifier FROM User WHERE Id =: UserInfo.getUserId()];

        Map<String, String> societyMap = new Map<String, String>{
            'MandatoUnipolSai' => 'SOC_1',
            'MandatoUniSalute' => 'SOC_4'
        };

        Map<String, List<NetworkUser__c>> societyNetworkMap = new Map<String, List<NetworkUser__c>>();
        List<NetworkUser__c> networkList = [SELECT Id, Preferred__c, IsActive__c, NetworkUser__c, Society__c, FiscalCode__c FROM NetworkUser__c WHERE FiscalCode__c = :usr.FederationIdentifier AND IsActive__c = TRUE ORDER BY Preferred__c DESC, Society__c];
        for(NetworkUser__c network : networkList) {
            if(network.Society__c == null || network.NetworkUser__c == null) {
                continue;
            }
            if(!societyNetworkMap.containsKey(network.Society__c)) {
                societyNetworkMap.put(network.Society__c, new List<NetworkUser__c>());
            }

            societyNetworkMap.get(network.Society__c).add(network);
        }

        //system.debug(JSON.serialize(societyNetworkMap));
        //system.debug(JSON.serialize(societyNetworkMap.get('SOC_1')));
        //system.debug(JSON.serialize(societyNetworkMap.get('SOC_4')));

        List<Tabs> tabsList = new List<Tabs>();
        for(Menu_Strumenti_Tabs__mdt menuStrumentiTabs : menuStrumentiTabsMap.values()) {

            if(mandatiAbilitati.contains(menuStrumentiTabs.Mandato_Abilitato__c)) {
                Tabs tabs = new Tabs();
                tabs.label = menuStrumentiTabs.MasterLabel;
                tabs.name = menuStrumentiTabs.DeveloperName;
                tabs.context = menuStrumentiTabs.Context_Target__c;
                tabs.userContext = societyNetworkMap.get(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)) != null ? societyNetworkMap.get(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)).get(0).NetworkUser__c : null;
                tabsList.add(tabs);
            }
        }

        //system.debug(JSON.serialize(tabsList));

        return tabsList;
    }

    public class Section {
        @AuraEnabled 
        public String label;
        @AuraEnabled 
        public String name;
        @AuraEnabled 
        public String context;
        @AuraEnabled 
        public Integer order;
    }

    @AuraEnabled(cacheable=true)
    public static List<Section> getSections(String context) {
        
        List<Section> sectionsList = new List<Section>();
        for(Menu_Strumenti_Section__mdt menuStrumentiSection : [SELECT Label, DeveloperName, Context__c, Order__c
                                                        FROM Menu_Strumenti_Section__mdt 
                                                        WHERE Context__c = :context ORDER BY Order__c]) {

            Section sections = new Section();
            sections.label = menuStrumentiSection.Label;
            sections.name = menuStrumentiSection.DeveloperName;
            sections.context = menuStrumentiSection.Context__c;
            sections.order = Integer.valueOf(menuStrumentiSection.Order__c);
            sectionsList.add(sections);
        }

        return sectionsList;
    }

    @AuraEnabled(cacheable=true)
    public static List<MenuStrumentiEngine.SectionWrapper> getTreeByContext(String context, String userContext) {
        return MenuStrumentiEngine.getTreeByContext(context, userContext);
    }

    @AuraEnabled(cacheable=true)
    public static List<String> getUserFavorite() {

        /*String favoriteList = [SELECT Menu_Strumenti_Preferiti__c FROM User WHERE Id = :UserInfo.getUserId()].Menu_Strumenti_Preferiti__c;
        
        if(favoriteList == null) return new List<String>();
        return (List<String>) JSON.deserialize(favoriteList, List<String>.class);*/

        List<Asset> assetList = [SELECT User__c, Category__c, Value__c FROM Asset WHERE User__c = :UserInfo.getUserId() AND Category__c = 'Preferiti'];
        
        if(assetList == null || assetList.isEmpty()) return new List<String>();

        List<String> favoriteList = new List<String>();
        for(asset ast : assetList) {
            favoriteList.add(ast.Value__c);
        }

        return favoriteList;
    }

    @AuraEnabled
    public static void setUserFavorite(String favoriteJSON) {
        
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        List<Asset> assetList = [SELECT User__c, Category__c, Value__c FROM Asset WHERE User__c = :UserInfo.getUserId() AND Category__c = 'Preferiti' AND RecordTypeId = :assetRtId];
        if(!assetList.isEmpty() && assetList != null) {
            Database.delete(assetList);
        }

        List<String> favoriteList = (List<String>) JSON.deserialize(favoriteJSON, List<String>.class);
        List<Asset> assetToInsert = new List<Asset>();
        for(String favorite : favoriteList) {
            assetToInsert.add(new Asset(
                Name = UserInfo.getUserId() + '_P_' + favorite,
                User__c = UserInfo.getUserId(),
                Category__c = 'Preferiti',
                Value__c = favorite
            ));
        }

        Database.insert(assetToInsert);
    }

     @AuraEnabled
    public static void removeUserFavorite(String favorite) {

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();
        
        List<Asset> assetList = [SELECT User__c, Category__c, Value__c FROM Asset WHERE 
                                User__c = :UserInfo.getUserId() AND 
                                Category__c = 'Preferiti' AND RecordTypeId = :assetRtId];
        
        if(!assetList.isEmpty() && assetList != null) {
            Asset astToRemove = null;
            for(Asset ast : assetList) {
                if(ast.Value__c == favorite) {
                    astToRemove = ast;
                    break;
                }
            }
            if(astToRemove != null) {
                Database.delete(astToRemove);
            }
        }
    }

    @AuraEnabled
    public static Map<String, String> getParamsForFei(String feiId) {
        String permissionSetName = [
            SELECT Id, UCA_Permission_Name__c FROM FEI_Settings__mdt 
            WHERE Label =: feiId AND Environment__c =: FEI_Environment__c.getInstance().Environment__c LIMIT 1].UCA_Permission_Name__c;
        
        String fiscalCode = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1].FederationIdentifier;
            return new Map<String, String>{'permissionSetName' => permissionSetName, 'fiscalCode' => fiscalCode};
    }
}