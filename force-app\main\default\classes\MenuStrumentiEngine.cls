public with sharing class MenuStrumentiEngine {
    
    public static final Integer MAX_VALUE = 99;

    public class Node implements Comparable {
        @AuraEnabled 
        public String label;
        @AuraEnabled 
        public String developerName;
        @AuraEnabled 
        public String parent;
        @AuraEnabled 
        public String context;
        @AuraEnabled 
        public String section;
        @AuraEnabled 
        public String type;       // 'menu' | 'fei' | 'redirect'
        @AuraEnabled 
        public String feiId;
        @AuraEnabled 
        public String redirectLink;
        @AuraEnabled
        public String redirectType;
        @AuraEnabled 
        public String profileCode;
        @AuraEnabled 
        public Boolean isLeaf;
        @AuraEnabled 
        public Integer order;
        @AuraEnabled 
        public Boolean expanded = false;
        @AuraEnabled 
        public Boolean isFavorite = false;
        @AuraEnabled 
        public String params;
        @AuraEnabled 
        public String requestType;
        @AuraEnabled 
        public List<Node> children = new List<Node>();

        public Integer compareTo(Object o) {
            Node other   = (Node)o;
            Integer thisO  = (order  != null) ? order  : MAX_VALUE;
            Integer otherO = (other.order != null) ? other.order : MAX_VALUE;

            if (thisO == otherO) return 0;
            return (thisO < otherO) ? -1 : 1;
        }
    }

    public class SectionWrapper implements Comparable {
        @AuraEnabled public String section;
        @AuraEnabled public Integer order;
        @AuraEnabled public List<Node> items;

        /** Ordine card: solo sul campo order; null => in coda */
        public Integer compareTo(Object o) {
            SectionWrapper other = (SectionWrapper)o;
            Integer thisO  = (order  != null) ? order  : 2147483647;
            Integer otherO = (other.order != null) ? other.order : 2147483647;

            /* ► stesso identico confronto */
            if (thisO == otherO) return 0;
            return (thisO < otherO) ? -1 : 1;
        }
    }

    public static List<SectionWrapper> getTreeByContext(String context, String userContext) {

        String userProfile = getUserProfileName();

        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                   DeveloperName,
                   Is_Active__c,
                   Parent__c,
                   Context__c,
                   Section__c,
                   Order__c,
                   Type__c,
                   FEI_ID__c,
                   Redirect_Link__c,
                   Redirect_Type__c,
                   Profile_Code__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  Is_Active__c = true AND Context__c = :context
        ];

        /* Mappa DeveloperName => Node */
        Map<String, Node> mapDev = new Map<String, Node>();
        for (Menu_Strumenti_Tree_Structure__mdt r : rows) {
            
            if(r.Profile_Code__c != null) {
                Boolean checkProfile = false;
                if(r.Profile_Code__c.contains(';')) {
                    List<String> profileCodesList = r.Profile_Code__c.split(';');

                    for(String profileCode : profileCodesList) {
                        if(userProfile.containsIgnoreCase(profileCode)) {
                            checkProfile = true;
                        }
                    }    
                } else {
                    if(userProfile.containsIgnoreCase(r.Profile_Code__c)) {
                        checkProfile = true;
                    }
                }

                if(!checkProfile) {
                    continue;
                }
            }

            Node n = new Node();
            n.label= r.MasterLabel;
            n.developerName = r.DeveloperName;
            n.parent = r.Parent__c;
            n.context = r.Context__c;
            n.section = r.Section__c;
            n.type = r.Type__c;
            n.feiId = r.FEI_ID__c;
            n.redirectLink = r.Redirect_Link__c;
            n.redirectType = r.Redirect_Type__c;
            n.profileCode = r.Profile_Code__c;
            n.isLeaf = (r.Type__c != 'MENU');
            n.order = Integer.valueOf(r.Order__c);
            mapDev.put(r.DeveloperName, n);
        }

        /* Collega figli → padre */
        Set<String> childKeys = new Set<String>();

        for (Menu_Strumenti_Tree_Structure__mdt r : rows) {
            if (!String.isBlank(r.Parent__c) && mapDev.containsKey(r.Parent__c)) {

                Node parent = mapDev.get(r.Parent__c);
                Node child  = mapDev.get(r.DeveloperName);

                parent.children.add(child);             
                childKeys.add(child.developerName);
            }
        }

        Map<String, List<Node>> bySection   = new Map<String, List<Node>>();
        Map<String, Integer>    sectionOrd  = new Map<String, Integer>();

        for (Node n : mapDev.values()) {
            if (childKeys.contains(n.developerName)) continue; 

            String key = String.isBlank(n.section) ? 'Altro' : n.section;
            if (!bySection.containsKey(key)) bySection.put(key, new List<Node>());
            bySection.get(key).add(n);

            Integer o = (n.order != null) ? n.order : MAX_VALUE;
            if (!sectionOrd.containsKey(key) || o < sectionOrd.get(key)) {
                sectionOrd.put(key, o);
            }
        }

        /* Ordina ogni lista (root + discendenti) */
        for (List<Node> lst : bySection.values()) {
            sortRecursively(lst);
        }

        /* Costruisci i SectionWrapper */
        List<SectionWrapper> result = new List<SectionWrapper>();
        for (String key : bySection.keySet()) {
            SectionWrapper w = new SectionWrapper();
            w.section = key;
            w.order   = sectionOrd.get(key);
            w.items   = bySection.get(key);
            result.add(w);
        }

        /* Ordina le sezioni e restituisci */
        result.sort();

        /* Rimuovi i nodi che non hanno nodi leaf */
        for (SectionWrapper w : result) {
            if (w.items != null) {
                // scorro all'indietro per poter rimuovere in place
                for (Integer i = w.items.size() - 1; i >= 0; i--) {
                    Node node = w.items.get(i);
                    if ((node.children == null || node.children.isEmpty()) && !node.isLeaf) {
                        w.items.remove(i);
                    }
                }
            }
        }

        if(userContext != null) {
            String sec;
            if(context == 'MENU_STRUMENTI_UNIPOL') {
                sec = 'UN_LINK_SERVIZI';
            } else if(context == 'MENU_STRUMENTI_UNISALUTE') {
                sec = 'US_LINK_SERVIZI';
            }
            SectionWrapper altreFunzioniWrapepr = getTreeFromExternalService(context, sec, MenuStrumentiService.parse(MenuStrumentiService.testParse()));
            result.add(altreFunzioniWrapepr);
        }

        System.debug(JSON.serialize(result));

        return result;
    }

    private static void sortRecursively(List<Node> nodeList) {
        if (nodeList == null) return;
        nodeList.sort();
        for (Node n : nodeList) sortRecursively(n.children);
    }

    private static String getUserProfileName() {
        return [SELECT Name FROM Profile WHERE Id IN (SELECT ProfileId FROM User WHERE Id = :UserInfo.getUserId())].Name;
    }

    /**
     * Converte il JSON del servizio esterno (Link a Servizi)
     * nel formato interno di MenuStrumentiEngine.
     * 
     * @param context     Contesto (es. "MENU_STRUMENTI_UNIPOL")
     * @param sectionName Nome logico della sezione (es. "LINK_SERVIZI")
     * @param jsonPayload JSON raw restituito dal servizio esterno
     * @return Lista di SectionWrapper con i nodi formattati
     */
    @AuraEnabled
    public static SectionWrapper getTreeFromExternalService(
        String context,
        String sectionName,
        MenuStrumentiService svc
    ) {
        // Costruisce il livello radice dall'array children
        List<Node> roots = new List<Node>();
        if (svc.children != null) {
            for (MenuStrumentiService.cls_children child : svc.children) {
                roots.add(convertServiceNode(child, context, sectionName));
            }
        }
        
        // Avvolge in una singola sezione
        SectionWrapper sw = new SectionWrapper();
        sw.section = sectionName;
        sw.order   = 0;
        sw.items   = roots;
        
        return sw;
    }

    /**
     * Ricorsione per convertire nodes esterni in Node interni
     */
    private static Node convertServiceNode(
        MenuStrumentiService.cls_children data,
        String context,
        String sectionName
    ) {
        Node n = new Node();
        n.developerName = data.id;
        n.label         = data.description;
        //n.feiId         = data.url;
        n.redirectLink  = data.url;
        n.params        = JSON.serialize(data.params);
        n.requestType   = data.verb;
        //n.parent        = data.parentId;
        n.context       = context;
        n.section       = sectionName;
        
        Boolean hasKids = (data.children != null && !data.children.isEmpty());
        n.isLeaf   = !hasKids;
        n.type     = n.isLeaf ? 'FEI' : 'MENU';
        n.order    = data.level;
        n.expanded = false;
        n.children = new List<Node>();
        
        // Ricorsione sui figli
        if (hasKids) {
            for (MenuStrumentiService.cls_children sub : data.children) {
                n.children.add(convertServiceNode(sub, context, sectionName));
            }
        }
        return n;
    }
}