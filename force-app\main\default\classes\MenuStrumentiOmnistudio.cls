global without sharing class MenuStrumentiOmnistudio implements System.Callable {
    
    private static final String GENERIC_ERROR = 'Si è verificato un errore';

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName.equals('getPreferiti')) {
                getPreferiti(inputs, output);
            } else {
                output.put('success', false);
                output.put('errorMessage', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }

        } catch (Exception e) {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
            result = false;
        }

        return result;
    }

    private void getPreferiti(Map<String, Object> inputs, Map<String, Object> output) {

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        List<Asset> assetList = [SELECT User__c, Category__c, Value__c FROM Asset WHERE User__c = :UserInfo.getUserId() AND Category__c = 'Preferiti' AND RecordTypeId = :assetRtId];

        if(assetList == null || assetList.isEmpty()) {
            output.put('success', true);
            output.put('result', null);
            return;
        }

        Set<String> apiNameString = new Set<String>();
        for(Asset ast : assetList) {
            apiNameString.add(ast.Value__c);
        }

        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                   DeveloperName,
                   Is_Active__c,
                   Parent__c,
                   Context__c,
                   Section__c,
                   Order__c,
                   Type__c,
                   FEI_ID__c,
                   Redirect_Link__c,
                   Redirect_Type__c,
                   Profile_Code__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  Is_Active__c = true AND DeveloperName IN :apiNameString
        ];

        if (rows != null && !rows.isEmpty()) {

            output.put('success', true);
            output.put('result', rows);
        } else {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
        }
    }
}