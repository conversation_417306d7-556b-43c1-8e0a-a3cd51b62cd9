global without sharing class UniLogIntegrationProcedureSubmit implements Callable {

    global final static String PARAM_INPUT = 'input';
	global final static String PARAM_OUTPUT = 'output';
	global final static String PARAM_OPTIONS = 'options';

    // additional input key-value pairs --> MA<PERSON><PERSON>ORY FIELDS
    global final static String INPUT_MESSAGE = 'Message';
    global final static String INPUT_PAYLOAD = 'Payload';
    public final static String INPUT_CLASSNAME = 'ClassName';
    public final static String INPUT_METHODNAME = 'MethodName';


    // FACULTATIVE FIELDS
    global final static String INPUT_REFID = 'RefId';
    global final static String INPUT_LOGGINGLEVEL = 'LoggingLevel';
    global final static String INPUT_LOGTYPE = 'LogType';


	global Object call(String action, Map<String, Object> args) {

        try{
		Map<String, Object> input = (Map<String, Object>) args.get(PARAM_INPUT);
		Map<String, Object> output = (Map<String, Object>) args.get(PARAM_OUTPUT);
		Map<String, Object> options = (Map<String, Object>) args.get(PARAM_OPTIONS);



		//submit error
        UniLogger logHandler = new UniLogger(
            // (string)input.get(INPUT_REFID),
            (string)input.get(INPUT_MESSAGE),
            (string)JSON.serializePretty(input.get(INPUT_PAYLOAD)),
            (string) input.get(INPUT_CLASSNAME),
            (string) input.get(INPUT_METHODNAME),
             'OS'
        );


        System.debug(LoggingLevel.DEBUG, 'uniqueName : ' + 'OS_' + input.get(INPUT_CLASSNAME) + '_' + input.get(INPUT_METHODNAME)) ;
        logHandler
            // .setUniqueName('OS_' + input.get(INPUT_CLASSNAME) + '_' + input.get(INPUT_METHODNAME))
            // .setLoggingLevel(LoggingLevel.valueof((string)input.get(INPUT_LOGGINGLEVEL)))
            .setLogSource(Unilogger.logSource.IP)
            .publishLog();

        if(!String.isBlank((string)input.get(INPUT_LOGTYPE))) logHandler.setLogType(UniLogger.LogType.valueOf((string) input.get(INPUT_LOGTYPE)) );
		return true;

        }

        catch(Exception exc){return false;}
	}
}