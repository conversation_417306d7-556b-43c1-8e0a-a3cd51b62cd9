public without sharing class UniLogger {

    public enum LogType{INTEGRATION, GENERIC, HANDLED_EXCEPTION} // same as Custom_Log__c recordType
    public enum LogSource{LWC, Apex, IP, Flow}
    IntegrationSettings__mdt intSetting;
    Log_Config__mdt config;

    //platform event instance
    public UniLogEvent__e logEvent;

    //public constructor
    public UniLogger(string message, Object payload, Integer lastCallingClassNumber) {

        // fields className and methodName are mandatory (used in CMDT configuration record)
        logEvent = new UniLogEvent__e();
        this.setLogType(LogType.GENERIC);
        this.setMessage(message);
        this.setPayload(payload);
        this.setUniqueName(lastCallingClassNumber);
        this.setRequestInfo();
        this.setWho(UserInfo.getUserId());
        this.setTimestampWithMilliSec(System.now());
        
        this.setExpirationTime();

        try{
            this.authorizedForPublishGenericAndException(logEvent.UniUniqueName__c);
        }
        catch(Exception exc){}

        if(payload instanceof Exception){
            System.debug(loggingLevel.DEBUG, 'UniLogger | constructor | payload instanceof Exception');
            parseException((exception)payload);
            setLogType(logType.HANDLED_EXCEPTION);
        }

    }

        //public constructor
    public UniLogger(string message, Object payload, string className, string methodName, string source) {

        System.debug(LoggingLevel.DEBUG, 'UniLogger | Constructor for OS | payload : ' + payload);
        // fields className and methodName are mandatory (used in CMDT configuration record)
        logEvent = new UniLogEvent__e();
        this.setLogType(LogType.GENERIC);
        this.setMessage(message);
        this.setPayload(payload);
        this.setUniqueName(className, methodName, source);
        this.setRequestInfo();
        this.setWho(UserInfo.getUserId());
        this.setTimestampWithMilliSec(System.now());
        
        this.setExpirationTime();

        try{
            this.authorizedForPublishGenericAndException(logEvent.UniUniqueName__c);
        }
        catch(Exception exc){}

        if(payload instanceof Exception){
            System.debug(loggingLevel.DEBUG, 'UniLogger | constructor | payload instanceof Exception');
            parseException((exception)payload);
            setLogType(logType.HANDLED_EXCEPTION);
        }

    }

    //public constructor for INTEGATION
    public UniLogger(string message,  httpRequest request, httpResponse response, String integrationId) {

        // fields className and methodName are mandatory (used in CMDT configuration record)
        logEvent = new UniLogEvent__e();

        this.setLogType(LogType.INTEGRATION);
        this.setMessage(message);
        this.parseCallout(request, response );
        this.setUniqueName(integrationId);
        this.setLogType(LogType.INTEGRATION);
        this.setRequestInfo();
        this.setWho(UserInfo.getUserId());
        this.setTimestampWithMilliSec(System.now());
        
        this.setExpirationTime();

        try{
            this.authorizedForPublishIntegration(integrationId);
        }
        catch(Exception exc){}

    }

    /**************
     * @description : publish the log
     */
    public Database.SaveResult publishLog(){

        // if(String.isBlank(logEvent.UniType__c)) assert.fail('you must specify a log recordType');
        // if(String.isBlank(logEvent.UniSource__c)) assert.fail('you must specify a log source');

        if(String.isBlank(logEvent.UniType__c)) logEvent.UniType__c = 'GENERIC';
        if(String.isBlank(logEvent.UniSource__c)) logEvent.UniSource__c = 'unspecified';

        if(Unilogger.getPlatformEventPercentageReached() > 90) return null;

        if(LogEvent.UniIsAuthorizedForPublish__c == true){
            System.debug(LoggingLevel.INFO, 'UniLogger | publishLog | publishing log : '+logEvent);
            Database.SaveResult myLogResult = EventBus.publish(logEvent); 
            System.debug(LoggingLevel.INFO, 'log published. SaveResult : ' + myLogResult);
            return myLogResult; 
        }
        else{
            System.debug(LoggingLevel.ERROR, 'UniLogger | publishLog | log is not authorized for publishing :'+ logEvent);
            System.debug(LoggingLevel.ERROR, 'UniLogger | publishLog | log is not authorized for publishing. Reasons is : '+logEvent);
        }
        return null;
    }

    public UniLogger authorizedForPublishGenericAndException(String uniqueName){

        if(LogEvent.UniType__c != 'GENERIC' && LogEvent.UniType__c != 'HANDLED_EXCEPTION' ){
            System.debug(LoggingLevel.ERROR, 'UniLogger | setIsAuthorizedForPublish | this method is meant for only GENERIC and HANDLED_EXCEPTION');
            return this;
        }
        if(Test.isRunningTest()){
            setIsAuthorizedForPublish(); 
            return this; // for testing purposes
        } 


            if(Unilogger.getPlatformEventPercentageReached() > 90){
                LogEvent.UniMessage__c = 'platform event limit is above 90%, log is not authorized for publication : ' + getPlatformEventPercentageReached();
                return null;
            } 

        try{

        
            if(Test.isRunningTest()) setIsAuthorizedForPublish();

            // fetch CMDT to verify if this log should be recorded        
            config = Log_Config__mdt.getInstance(logEvent.UniUniqueName__c);
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | /////////////////////////////////////////////////////////////////////////////////////');
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | /////////////// UniqueName for "LogEvent.UniqueName : ' + logEvent?.UniUniqueName__c);
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | /////////////////////////////////////////////////////////////////////////////////////');
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | /////////////// UniqueName for "Log Config" CMDT : ' + config?.DeveloperName );
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | ////////////////////////////////////////////////////////////////////////////////////////');
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishGenericAndException | configuration record for publishing : '+ config);
            if(config?.PublishLog__c == true){
                setIsAuthorizedForPublish();
            } 
            else{
                LogEvent.UniMessage__c = 'log is not authorized for publication in CMDT : ' + config;
            }
        }
        catch(Exception exc){
            System.debug(LoggingLevel.ERROR, 'problem with fetching the CMDT');
        }
        
        
        return this;
    }

    public UniLogger authorizedForPublishIntegration(String integrationId){

        if(LogEvent.UniType__c != 'INTEGRATION'){
            System.debug(LoggingLevel.ERROR, 'UniLogger | setIsAuthorizedForPublish | this method is meant for only INTEGRATION');
            return this;
        }
        if(Test.isRunningTest()){
            setIsAuthorizedForPublish(); 
            return this; // for testing purposes
        } 

        if(Unilogger.getPlatformEventPercentageReached() > 90){
            LogEvent.UniMessage__c = 'platform event limit is above 90%, log is not authorized for publication : ' + getPlatformEventPercentageReached();
            return null;
        } 

        try{
            List<IntegrationSettings__mdt> integrationSettingListExecute = [
                SELECT Endpoint__c, Header__c, Method__c, NamedCredential__c, TimeoutMs__c, IntegrationId__c, TemplateJSONBody__c, PublishLog__c, Log_Lifetime__c
                FROM IntegrationSettings__mdt
                WHERE IntegrationId__c = :integrationId
                ORDER BY Version__c DESC];

            if(integrationSettingListExecute.isEmpty())     setMessage('no integration setting found');
            if(integrationSettingListExecute.size() != 1)   setMessage('several integration settings found');
            intSetting = integrationSettingListExecute[0];

            
            
            // fetch CMDT to verify if this log should be recorded        
            IntegrationSettings__mdt config = IntegrationSettings__mdt.getInstance(integrationId);
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishIntegration | /////////////////////////////////////////////////////////////////////////////////////');
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishIntegration | /////////////// UniqueName for "Log Config" CMDT : ' + integrationId);
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishIntegration | ////////////////////////////////////////////////////////////////////////////////////////');
            System.debug(LoggingLevel.INFO, 'UniLogger | authorizedForPublishIntegration | configuration record for publishing : '+ intSetting);
            if(intSetting?.PublishLog__c == true){
                setIsAuthorizedForPublish();
            } 
            else{
                LogEvent.UniMessage__c = 'log is not authorized for publication in CMDT : ' + config;
            }
        } catch(Exception exc){
            System.debug(LoggingLevel.ERROR, 'problem with fetching the CMDT');
        }
        
        
        return this;
    }




    

    	/*********************************************************************************************
	 * @Description : set the log exception from an Exception object
	 *********************************************************************************************/
	public UniLogger parseException(Exception exc) {
		return this
            .setExceptionMessage(exc.getMessage())
			.setExceptionCause(String.ValueOf(exc.getCause()))
			.setExceptionLineNumber(String.ValueOf(exc.getLineNumber()))
			.setExceptionTypeName(exc.getTypeName())
			.setStackTraceString(exc.getStackTraceString());
	}

    public Unilogger parseCallout(HttpRequest myRequest, HttpResponse myResponse){
        return this
            .setEndPoint(myRequest.getEndpoint())
            // .setRequestHeader(myRequest.getHeader())
            .setMethod(myRequest.getMethod())
            .setRequestBody(myRequest.getBody())
            .setResponseBody(myResponse.getBody())
            .setResponseHeader(myResponse)
            .setStatusCode(myResponse.getStatusCode())
            .setStatus(myResponse.getStatus());


    }


    ////////////////////////////////////////////////////////////////
    ///////////////////// FACTORY //////////////////////////////////
    ////////////////////////////////////////////////////////////////

    public static void submitLog(string message, Object payload, System.LoggingLevel myLogLevel, string refId) {

        UniLogger logHandler = new UniLogger(
            message, 
            payload, 
            5);

        if(!String.isBlank(refId)) loghandler.setReferenceIdentifier(refId);

        loghandler
            .setLoggingLevel(myLogLevel)
            .setLogSource(UniLogger.LogSource.Apex)
            .publishLog();
    }
    


    // with 2 inputs
    public static void  writeError( string message, Object payload){
        submitLog( message, payload, System.LoggingLevel.ERROR, null);
    }

    public static void  writeWarn( string message, Object payload){
        submitLog( message, payload, System.LoggingLevel.WARN, null);
    }

    public static void writeDebug( string message, Object payload){
        submitLog( message, payload, System.LoggingLevel.DEBUG, null);
    }

    public static void writeInfo( string message, Object payload){
        submitLog(message, payload, System.LoggingLevel.INFO, null);
    }



    // with 3 inputs
    public static void  writeError( string message, Object payload, String refId){
        submitLog( message, payload, System.LoggingLevel.ERROR, refId);
    }

    public static void  writeWarn( string message, Object payload, String refId){
        submitLog( message, payload, System.LoggingLevel.WARN, refId);
    }

    public static void writeDebug( string message, Object payload, String refId){
        submitLog( message, payload, System.LoggingLevel.DEBUG, refId);
    }

    public static void writeInfo( string message, Object payload, String refId){
        submitLog(message, payload, System.LoggingLevel.INFO, refId);
    }

    /////////////////////////////////////////////
    ////////////////////////////////////////////////
    ////////////////////////////////////////////////

    public UniLogger setUniqueName(Integer lastCallingClassNumber){
        logEvent.UniUniqueName__c = getCallingClassName(lastCallingClassNumber);
        return this;
    }

    // for overriding the stacktrace method 
    public UniLogger setUniqueName(String uniqueName){
        logEvent.UniUniqueName__c = uniqueName;
        return this;
    }

        // for overriding the stacktrace method 
    public UniLogger setUniqueName(String className, String methodName, string source){
        logEvent.UniUniqueName__c =  source + '_' + className + '_' + methodName;
        return this;
    }




    // credits to https://salesforce.stackexchange.com/questions/21168/get-the-caller-class-name-in-apex-without-passing-parameter
    // this method fetches the calling class and method. 
    // lastCallingMethodHistory is sequence of execution in descending order (0 is last, then 1 etc)
    //   --> a priori number=2 to get the last calling class outside the logger
    public String getCallingClassName(Integer lastCallingMethodHistory) {

        // strack trace, ordered by execution sequence in descending way (last call is first in list)
        String[] trace = new HandledException().getStackTraceString().split('\n');
        System.debug(LoggingLevel.DEBUG, 'UniLogger | getCallingClassName | stacktrace : ' + trace);

        try{
            String fullAddress = trace[lastCallingMethodHistory].split(':')[0].replace('.', '_');
            system.debug('UniLogger | getCallingClassName | fullAddress from stackTrance : ' + fullAddress);
            // String class_method = fullName.split('\\.')[1] + '_' + fullName.split('\\.')[2];
            return fullAddress; // with class.className.classMethod
        }
        catch(Exception exc){
            System.debug('exception ' + exc);
        }

        return 'Unknown';
    }

    public static Integer getPlatformEventPercentageReached(){
        Map<String,System.OrgLimit> currentLimitsMap = OrgLimits.getMap();
        System.OrgLimit platformEvent = currentLimitsMap.get('DailyDeliveredPlatformEvents');
        return (platformEvent.getValue() / platformEvent.getLimit())*100; 
    }

    public static Integer getFileStoragePercentageReached(){
        Map<String,System.OrgLimit> currentLimitsMap = OrgLimits.getMap();
        System.OrgLimit platformEvent = currentLimitsMap.get('FileStorageMB');
        return (platformEvent.getValue() / platformEvent.getLimit())*100; 
    }

    ////////////////////////////////////////////////////////////////
    ////////////////////// BASE METHODS ////////////////////////////
    ////////////////////////////////////////////////////////////////

        // TODO : handle custom_log__c recordType
    	/*********************************************************************************************
	 * @Description : Set the log record type developer name as "Error"
	 *********************************************************************************************/
	public UniLogger setLogType(logType myType) {
		logEvent.UniType__c = myType.toString();
        if(myType == logType.HANDLED_EXCEPTION){
            this.setLoggingLevel(System.loggingLevel.Error);
        } 
        return this;
	}

    // TODO : handle custom_log__c recordType
	/*********************************************************************************************
	 * @Description : Set the log record type developer name as "Integration Log"
	 *********************************************************************************************/
	public UniLogger setLogSource(logSource mylogSource) {
		logEvent.UniSource__c = mylogSource.toString();
        return this;
	}

    public UniLogger setRequestInfo(){
        Request reqInfo = Request.getCurrent();
        logEvent.UniRequestId__c = reqInfo.getRequestId().toString();
        logEvent.UniQuiddity__c = reqInfo.getQuiddity().toString();
        return this;
    }

    // // TODO : extend this with logSource
    // public String getLocationName(){
    //     return logEvent.UniClassName__c + '_' + logEvent.UniMethodName__c;
    // }

    	/*********************************************************************************************
	 * @Description : set Name of the class where the error was caught
	 *********************************************************************************************/
	public UniLogger setClassName(String className) {
		logEvent.UniClassName__c = className;
		return this;
	}

	/*********************************************************************************************
	 * @Description : set the name of the method where the error was caught
	 *********************************************************************************************/
	public UniLogger setMethod(String methodName) {
		logEvent.UniMethodName__c = methodName;
		return this;
	}


    public static final Integer STANDARD_LIFETIME = 5;
    public UniLogger setExpirationTime(){

        if(Test.isRunningTest()) return this;

        If(logEvent.UniType__c == 'INTEGRATION'){
            If(intSetting?.Log_Lifetime__c != null)
                logEvent.UniExpirationDate__c = Date.today().addDays(Integer.valueOf(intSetting.Log_Lifetime__c));
            else{
                logEvent.UniExpirationDate__c = Date.today().addDays(STANDARD_LIFETIME);
            }
        }
        else {

            If(config?.Lifetime_Retention__c != null)
                logEvent.UniExpirationDate__c = Date.today().addDays(Integer.valueOf(config.Lifetime_Retention__c));
            else{
                logEvent.UniExpirationDate__c = Date.today().addDays(STANDARD_LIFETIME);
            }
        }
        return this;
    }

    // not done because no way to access all keys
    // public UniLogger setRequestHeader(HttpRequest myRequest) {
	// 	logEvent.UniRequestHeader__c = requestHeader;
	// 	return this;
	// }

    public UniLogger setResponseHeader(HttpResponse myResponse) {

        Map<string, string> headers = new map<string, string>();

        for(String key : myResponse.getHeaderKeys()){
            headers.put(key,myResponse.getHeader(key));
        } 
		logEvent.UniResponseHeader__c = headers.toString();
		return this;
	}

	/*********************************************************************************************
	 * @Description : set the URL used in the callout
	 *********************************************************************************************/
	public UniLogger setEndPoint(String endPoint) {
		logEvent.UniEndPoint__c = endPoint;
		return this;
	}

	/*********************************************************************************************
	 * @Description : set the date and time when the event took place
	 *********************************************************************************************/
	public UniLogger setTimestampWithMilliSec(DateTime eventDateTime) {
		logEvent.UniTimestampEpoch__c = eventDateTime.getTime().format(); // store epoch as String of Long
        logEvent.UniTimestampDateTimeMilliSec__c = eventDateTime.format('MM/dd/yyyy HH:mm:ss', 'CET') + ' ' +  String.valueOf(eventDateTime.millisecond()); //store as String of datetime + millisec
		return this;
	}


    	/*********************************************************************************************
	 * @Description : set the exception cause of the error
	 *********************************************************************************************/
	public UniLogger setExceptionCause(String exceptionCause) {
		logEvent.UniExceptionCause__c = exceptionCause;
		return this;
	}

	/*********************************************************************************************
	 * @Description : set the exception line number of the error
	 *********************************************************************************************/
	public UniLogger setExceptionLineNumber(String exceptionLineNumber) {
		logEvent.UniExceptionLineNumber__c = exceptionLineNumber;
		return this;
	}

	/*********************************************************************************************
	 * @Description : set the exception type of the error
	 *********************************************************************************************/
	public UniLogger setExceptionTypeName(String exceptionTypeName) {
		logEvent.UniExceptionTypeName__c = exceptionTypeName;
		return this;
	}


    	/*********************************************************************************************
	 * @Description : set the field where the error was thrown
	 *********************************************************************************************/
	public UniLogger setFieldName(String fieldName) {
		logEvent.UniFieldName__c = fieldName;
		return this;
	}


        	/*********************************************************************************************
	 * @Description : set the message of the error or the event
	 *********************************************************************************************/
	public UniLogger setPayload(Object payload) {

        // TODO : get the max length of custom_log__c object and truncate if needed
        // logEvent.UniPayload__c = (string)payload;

        Integer maxLongTextCharacs = 101000;
        String  payloadAsString = String.valueOf(payload);
        // Integer payloadLength = payloadAsString?.length();
        // Integer remainingCharacsToSave = payloadAsString.size();
        // Integer characsAlreadySavedAmount = 0;
        System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | length of payload : ' + payloadAsString?.length());



        if(String.isBlank(payloadAsString)){
            System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | payload empty');
            logEvent.UniPayload__c = null;
            return this;
        } 


        logEvent.UniPayload__c = payloadAsString.abbreviate(maxLongTextCharacs);
        if(payloadAsString?.length() <  maxLongTextCharacs){
            System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | payload < 101k charac --> normal and stored in Payload');
            logEvent.UniPayload__c = payloadAsString;
        }
        else {
            System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | payload > 101k charac --> too big and stored as file');
            logEvent.UniPayload__c = '{}'; // this value is needed to activate the LWC for JSON
            if(getFileStoragePercentageReached() > 90){
                System.debug(LoggingLevel.WARN, 'file storage above 90% --> file of payload is not stored');
                return this;
            }
            ContentVersion cv = new ContentVersion();
            cv.PathOnClient = 'bigPayloadDump.txt';
            cv.versionData = Blob.valueOf(payloadAsString);
            insert cv; // ! not bulkified
            logEvent.UniPayloadAsFileId__c = cv.id;
            system.debug(LoggingLevel.DEBUG, 'payloadAsString > last 10 charac : ' + payloadAsString.right(10));
            System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | ContentVersionId of payload : ' + cv.id);
        }
        return this;
    }


    // this logic was based on  String.abbreviate(max, offset) but it does not seem to work for big numbers
    // if(payloadLength > 1*maxLongTextCharacs) logEvent.UniPayloadBig1__c = payloadAsString.abbreviate(maxLongTextCharacs*2, maxLongTextCharacs);
    // if(payloadLength > 2*maxLongTextCharacs) logEvent.UniPayloadBig2__c = payloadAsString.abbreviate(maxLongTextCharacs*3, maxLongTextCharacs*2);
    // if(payloadLength > 3*maxLongTextCharacs) logEvent.UniPayloadBig3__c = payloadAsString.abbreviate(maxLongTextCharacs*4, maxLongTextCharacs*3);

    // System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | UniPayload length : ' + logEvent.UniPayload__c.length());
    // System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | UniPayloadBig1__c length : ' + logEvent.UniPayloadBig1__c?.length());
    // System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | UniPayloadBig2__c length : ' + logEvent.UniPayloadBig2__c?.length());
    // System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | UniPayloadBig3__c length : ' + logEvent.UniPayloadBig3__c?.length());


    // this logic was based on String.right(length) but it does not seem to work for big numbers
    //     String remainingCharacs = truncatePayload(payloadAsString, maxLongTextCharacs);
    //     System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | length of payload remaining1 : ' + payloadAsString?.length());
    //     if(remainingCharacs?.length() > 0){
    //         logEvent.UniPayloadBig1__c = remainingCharacs?.abbreviate(maxLongTextCharacs);
    //         String remainingCharacs1 = truncatePayload(remainingCharacs, maxLongTextCharacs);
    //         System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | length of payload remaining2 : ' + payloadAsString?.length());
    //         if(remainingCharacs1?.length() > 0){
    //             logEvent.UniPayloadBig2__c = remainingCharacs1.abbreviate(maxLongTextCharacs);
    //             String remainingCharacs2 = truncatePayload(remainingCharacs1, maxLongTextCharacs);
    //             System.debug(LoggingLevel.DEBUG, 'UniLogger | setPayload | length of payload remaining3 : ' + payloadAsString?.length());
    //             if(remainingCharacs2?.length() > 0){
    //                 logEvent.UniPayloadBig3__c = remainingCharacs2?.abbreviate(maxLongTextCharacs);
    //                 String remainingCharacs3 = truncatePayload(remainingCharacs2, maxLongTextCharacs);
    //                 if(remainingCharacs2?.length() > 0) Assert.fail('payload is bigger than 400.000 characs');
    //             }
    //         }
    //     }
	// 	return this;
	// }

    // // string.right(length) method does not seem to work. Maybe because length is too big...
    // public String truncatePayload(String  payloadToTruncate, Integer max){
    //     // Integer longTextCharacsMax = 101000;
    //     // String characsAbbreviated = remainingCharacs.abbreviate(longTextCharacs);
    //     Integer remainingSize;
    //     if(payloadToTruncate?.length() - max <= 0) remainingSize = 0;
    //     else if(payloadToTruncate?.length() - max > 0) remainingSize = payloadToTruncate?.length() - max;
    //     System.debug(LoggingLevel.DEBUG, 'UniLogger | payloadToTruncate | remainingSize : ' + remainingSize);
    //     String remainingPayload = payloadToTruncate.right(remainingSize);
    //     System.debug(LoggingLevel.DEBUG, 'UniLogger | payloadToTruncate | remainingPayload : ' + remainingPayload);
        
    //     return remainingPayload;
    // }


    	/*********************************************************************************************
	 * @Description : set the message of the error or the event
	 *********************************************************************************************/
	public UniLogger setMessage(String message) {


        // TODO : get the max length of custom_log__c object and truncate if needed
        // Integer fieldLength = Schema.SObjectType.GEN_Log__c.fields.GEN_Message__c.getLength();
        // logEvent.UniMessage__c = message.abbreviate(fieldLength);
        logEvent.UniMessage__c = message;

		return this;
	}

	// /*********************************************************************************************
	//  * @Description : set the record type developer name of the error. This will be used to find
	//  *                and apply the correct record type
	//  *********************************************************************************************/
	// public UniLogger setRecordTypeDevName(String recTypeDevName) {
	// 	logEvent.UniRecordTypeDevName__c = recTypeDevName;
	// 	return this;
	// }

	/*********************************************************************************************
	 * @Description : set the request body of the request
	 *********************************************************************************************/
	public UniLogger setRequestBody(String requestBody) {

        Integer PARAM_REQUEST_BODY_LENGTH = 35000;

        // TODO : get the max length of custom_log__c object and truncate if needed
        // Integer fieldLength = Schema.SObjectType.GEN_Log__c.fields.GEN_RequestBody__c.getLength();
        // logEvent.GEN_RequestBody__c = requestBody.abbreviate(fieldLength);
        logEvent.UniRequestBody__c = requestBody.abbreviate(PARAM_REQUEST_BODY_LENGTH);

		return this;
	}

    	/*********************************************************************************************
	 * @Description : set the response body returned by the callout
	 *********************************************************************************************/
	public UniLogger setResponseBody(String responseBody) {

        Integer PARAM_RESPONSE_BODY_LENGTH = 35000;
        // TODO : get the max length of custom_log__c object and truncate if needed
        // Integer fieldLength = Schema.SObjectType.Custom_Log__c.fields.UniResponseBody__c.getLength();
        // logEvent.GEN_ResponseBody__c = responseBody.abbreviate(fieldLength);
        logEvent.UniResponseBody__c = responseBody.abbreviate(PARAM_RESPONSE_BODY_LENGTH);

		return this;
	}



    	/*********************************************************************************************
	 * @Description : set the stack trace string of the error
	 *********************************************************************************************/
	public UniLogger setStackTraceString(String stackTraceString) {
		logEvent.UniExceptionStackTraceString__c = stackTraceString;
		return this;
	}

        	/*********************************************************************************************
	 * @Description : set the stack trace string of the error
	 *********************************************************************************************/
	public UniLogger setExceptionMessage(String exceptionMessage) {
		logEvent.UniExceptionMessage__c = exceptionMessage;
		return this;
	}

    	/*********************************************************************************************
	 * @Description : set the status of the callout
	 *********************************************************************************************/
	public UniLogger setStatus(String status) {
		logEvent.UniStatus__c = status;
		return this;
	}

        	/*********************************************************************************************
	 * @Description : set the statusCode of the callout
	 *********************************************************************************************/
	public UniLogger setStatusCode(Integer statusCode) {
		logEvent.UniStatusCode__c = String.valueof(statusCode);
		return this;
	}

    	/*********************************************************************************************
	 * @Description : set the type of log
	 *********************************************************************************************/
	// public UniLogger setType(String type) {
	// 	logEvent.UniType__c = type;
	// 	return this;
	// }

        	/*********************************************************************************************
	 * @Description : set the unique reference identifier
	 *********************************************************************************************/
	public UniLogger setLoggingLevel(System.loggingLevel myloggingLevel) {
		logEvent.UniLevel__c = myloggingLevel.toString();
		return this;
	}

    	/*********************************************************************************************
	 * @Description : set the unique reference identifier
	 *********************************************************************************************/
	public UniLogger setReferenceIdentifier(String refId) {
		logEvent.UniReferenceId__c = refId;
		return this;
	}

    // /*********************************************************************************************
	//  * @Description : set the date and time when the log took place
	//  *********************************************************************************************/
	// public UniLogger setEventTimestamp(DateTime logTimestamp) {
	// 	logEvent.UniTimestampEpoch__c = logTimestamp.getTime();
	// 	return this;
	// }

    /*********************************************************************************************
	 * @Description : set a user in the log
	 *********************************************************************************************/
	public UniLogger setWho(String who) {
		logEvent.UniWho__c = who;
		return this;
	}

    public UniLogger setIsAuthorizedForPublish(){
        LogEvent.UniIsAuthorizedForPublish__c = true;
        return this;
    }


    /****************
     * helper for LWC compo to handle JSON
     */
    @AuraEnabled(cacheable=true)
    public static String getRelatedFilesByRecordId(String recordId) {

        // Get ContentDocumentLink       
        String docLink = [SELECT ContentDocumentId 
                    FROM ContentDocumentLink 
                    WHERE LinkedEntityId = :recordId
                    LIMIT 1].ContentDocumentId;
    
        ContentVersion doc = [SELECT VersionData 
                              FROM ContentVersion 
                              WHERE ContentDocumentId = :docLink
                              ORDER BY CreatedDate DESC 
                              LIMIT 1];

        return doc.VersionData.toString();
    }


    // public static string epochToDateTimeMilliSec(String epochAsString){
    //     Long epochAsLong = Long.valueOf(epochAsString);
    //     DateTime myTimeStamp = DateTime.newInstance(epochAsLong);
    //     String dateTimeMilliSec = myTimeStamp.format('MM/dd/yyyy HH:mm:ss', 'CET') + ' ' + myTimeStamp.millisecond();
    //     return dateTimeMilliSec;
    // }
}