global without sharing class UniUtils implements System.Callable {

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');
        return invokeMethod(action, input, output, options);
    }

    public Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){

        try {
            if (methodName.equals('populatePicklistCompany')){
                populatePicklistCompany(inputMap, outMap, options);
            }  
            if (methodName.equals('societyPicklist')) {
                societyPicklist(inputMap, outMap, options);
            }
        }catch(Exception e) {
            System.debug(LoggingLevel.ERROR, 'Exception: ' + e.getMessage() + ' ' + e.getStackTraceString());
        }
        return true;
    }

    private void populatePicklistCompany(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('%%inputMap: ' + inputMap);
        List<Map<String, Object>> optionList = new List<Map<String, Object>>();
        Set<String> addedValues = new Set<String>();

        Map<String, String> allowedMandates = new Map<String, String>{
            'MandatoUnipolSai' => 'SOC_1',
            'MandatoUniSalute' => 'SOC_4',
            'MandatoUnipolRental' => 'UnipolRental',
            'MandatoUnipolTech' => 'UnipolTech'
        };

        List<User> user = new List<User>();
        if (Schema.sObjectType.User.isAccessible()) {
            user = [SELECT Id, IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        }
        if (user.isEmpty() || user[0].IdAzienda__c == null) {
            outMap.put('options', optionList);
            return;
        }

        Set<String> userPermNames = new Set<String>();
        if (Schema.sObjectType.PermissionSetAssignment.isAccessible()) {
            for (PermissionSetAssignment psa : [
                SELECT PermissionSet.Name FROM PermissionSetAssignment
                WHERE AssigneeId = :UserInfo.getUserId() AND PermissionSet.Name IN :allowedMandates.keySet()
            ]) {
                userPermNames.add(psa.PermissionSet.Name);
            }
        }

        if (userPermNames.contains('MandatoUnipolRental')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolRental', 'value' => 'UnipolRental'});
            addedValues.add('UnipolRental');
        }

        if (userPermNames.contains('MandatoUnipolTech')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolTech', 'value' => 'UnipolTech'});
            addedValues.add('UnipolTech');
        }

        List<FinServ__AccountAccountRelation__c> accRelation = new List<FinServ__AccountAccountRelation__c>();
        if (Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible()) {
            accRelation = [
                SELECT FinServ__RelatedAccount__r.Name, FinServ__RelatedAccount__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c = :user[0].IdAzienda__c
                AND RecordType.DeveloperName = 'AgencySociety'
            ];
        }
        for (FinServ__AccountAccountRelation__c acc : accRelation) {
            String extId = acc.FinServ__RelatedAccount__r.ExternalId__c;
            String label = acc.FinServ__RelatedAccount__r.Name;
            for (String perm : userPermNames) {
                if (allowedMandates.get(perm) == extId && !addedValues.contains(label)) {
                    optionList.add(new Map<String, Object>{'name' => label, 'value' => label});
                    addedValues.add(label);
                }
            }
        }
        system.debug('%%optionList: ' + optionList);
        outMap.put('options', optionList);
    }

    public void societyPicklist(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug(inputMap);
        List<Map<String, String>> optionsList = new List<Map<String, String>>();
        User u = [SELECT id, FiscalCode__c, IdAzienda__c, Profile.Name FROM User WHERE id =: UserInfo.getUserId()];
        System.debug('User Id: ' + u.id);
        if(u.Profile.Name == 'System Administrator') {
            Map<String, String> optMap1 = new Map<String, String>();
            optMap1.put('name', 'unipolsai');
            optMap1.put('value', 'Unipol');
            optionsList.add(optMap1);
            Map<String, String> optMap2 = new Map<String, String>();
            optMap2.put('name', 'unisalute');
            optMap2.put('value', 'Unisalute');
            optionsList.add(optMap2);
            outMap.put('options', optionsList);
        } else {
            Set<String> societySet = new Set<String>();
            for(NetworkUser__c nu : [select Id, Agency__c, ExternalId__c, FiscalCode__c, IsActive__c, NetworkUser__c, PermissionSets__c, Profile__c, Role__c, Society__c, User__c from NetworkUser__c where FiscalCode__c =: u.FiscalCode__c and IsActive__c = true and Agency__c =: u.IdAzienda__c ]) {
                societySet.add(nu.Society__c);
            }
            System.debug(societySet);    
            for(String society : societySet) {
                Map<String, String> optMap = new Map<String, String>();
                if(society == 'SOC_1') {
                    optMap.put('name', 'unipolsai');
                    optMap.put('value', 'Unipol');
                    optionsList.add(optMap);
                } else if(society == 'SOC_4'){
                    optMap.put('name', 'unisalute');
                    optMap.put('value', 'Unisalute');
                    optionsList.add(optMap);
                } else continue;
            }
            outMap.put('options', optionsList);
        }
    }
}