@IsTest
private class uniUtilsTest {

    @IsTest
    static void testCallableWithValidMethod() {
        Account relatedAcc = new Account(Name = 'UnipolSai', ExternalId__c = 'SOC_1');
        Account relatedAcc2 = new Account(Name = 'Unisalute', ExternalId__c = 'SOC_4');
        insert new List<Account>{relatedAcc, relatedAcc2};

        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        User testUser = new User(
            Alias = 'tuser',
            Email='<EMAIL>',
            EmailEncodingKey='UTF-8',
            LanguageLocaleKey='en_US',
            LocaleSidKey='en_US',
            ProfileId = p.Id,
            TimeZoneSidKey='Europe/Paris',
            UserName='<EMAIL>',
            LastName='Test',
            IdAzienda__c = relatedAcc.Id
        );
        insert testUser;

        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        Id accountAgencyRecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
        FinServ__AccountAccountRelation__c accAccRelClienteAgency1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = relatedAcc.Id,
            FinServ__RelatedAccount__c = relatedAcc2.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = accountAgencyRecordTypeId,
            FinServ__ExternalId__c = 'PRLGRL95L07F704X_SOC_1_AGE_1'
        );
        insert accAccRelClienteAgency1;

        System.runAs(testUser) {
            // Dynamically fetch real permission sets from org
            List<PermissionSet> liveMandates = [
                SELECT Id, Name FROM PermissionSet
                WHERE Name IN ('MandatoUnipolRental', 'MandatoUniSalute', 'MandatoUnipolSai', 'MandatoUnipolTech')
            ];

            List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
            for (PermissionSet ps : liveMandates) {
                psaList.add(new PermissionSetAssignment(
                    AssigneeId = testUser.Id,
                    PermissionSetId = ps.Id
                ));
            }
            if (!psaList.isEmpty()) insert psaList;

            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outputMap = new Map<String, Object>();
            Map<String, Object> optionsMap = new Map<String, Object>();

            Map<String, Object> args = new Map<String, Object>{
                'input' => inputMap,
                'output' => outputMap,
                'options' => optionsMap
            };

            uniUtils utilsInstance = new uniUtils();
            Object result = utilsInstance.call('populatePicklistCompany', args);

            System.assertEquals(true, result, 'Method should return true');
            System.assert(outputMap.containsKey('options'), 'Output should contain picklist options');
        }
    }

    @IsTest
    static void testCallableWithInvalidMethod() {
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>(),
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        uniUtils utilsInstance = new uniUtils();
        Object result = utilsInstance.call('nonExistentMethod', args);
        System.assertEquals(true, result, 'Should return true for unknown method (graceful error handling)');
    }

    @IsTest
    static void testUserWithoutAzienda() {
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        User testUser = new User(
            Alias = 'nouser',
            Email='<EMAIL>',
            EmailEncodingKey='UTF-8',
            LanguageLocaleKey='en_US',
            LocaleSidKey='en_US',
            ProfileId = p.Id,
            TimeZoneSidKey='Europe/Paris',
            UserName='<EMAIL>',
            LastName='NullAzienda'
        );
        insert testUser;

        System.runAs(testUser) {
            Map<String, Object> args = new Map<String, Object>{
                'input' => new Map<String, Object>(),
                'output' => new Map<String, Object>(),
                'options' => new Map<String, Object>()
            };

            uniUtils utilsInstance = new uniUtils();
            utilsInstance.call('populatePicklistCompany', args);

            System.assert(args.get('output') != null, 'Output should exist');
            System.assertEquals(0, ((List<Object>) ((Map<String, Object>) args.get('output')).get('options')).size(), 'Options should be empty if IdAzienda__c is missing');
        }
    }

    @IsTest
    static void testUserWithMultipleMandates() {
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        Account accRental = new Account(Name = 'UnipolRental', ExternalId__c = 'UnipolRental');
        Account accRental2 = new Account(Name = 'UnipolTech', ExternalId__c = 'UnipolTech');
        insert new List<Account>{accRental, accRental2};

        User testUser = new User(
            Alias = 'mu',
            Email='<EMAIL>',
            EmailEncodingKey='UTF-8',
            LanguageLocaleKey='en_US',
            LocaleSidKey='en_US',
            ProfileId = p.Id,
            TimeZoneSidKey='Europe/Paris',
            UserName='<EMAIL>',
            LastName='MandatesUser',
            IdAzienda__c = accRental.Id
        );
        insert testUser;

        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        RecordType rt = [SELECT Id FROM RecordType WHERE DeveloperName = 'AgencySociety' AND SObjectType = 'FinServ__AccountAccountRelation__c' LIMIT 1];

        insert new List<FinServ__AccountAccountRelation__c>{
            new FinServ__AccountAccountRelation__c(
                FinServ__Account__c = testUser.IdAzienda__c,
                FinServ__RelatedAccount__c = accRental2.Id,
                RecordTypeId = rt.Id,
                FinServ__Role__c = role.Id
            ),
            new FinServ__AccountAccountRelation__c(
                FinServ__Account__c = testUser.IdAzienda__c,
                FinServ__RelatedAccount__c = accRental2.Id,
                RecordTypeId = rt.Id,
                FinServ__Role__c = role.Id
            )
        };

        System.runAs(testUser) {
            List<PermissionSet> liveMandates = [
                SELECT Id, Name FROM PermissionSet
                WHERE Name IN ('MandatoUnipolRental', 'MandatoUnipolTech')
            ];

            List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
            for (PermissionSet ps : liveMandates) {
                psaList.add(new PermissionSetAssignment(
                    AssigneeId = testUser.Id,
                    PermissionSetId = ps.Id
                ));
            }
            if (!psaList.isEmpty()) insert psaList;

            Map<String, Object> args = new Map<String, Object>{
                'input' => new Map<String, Object>(),
                'output' => new Map<String, Object>(),
                'options' => new Map<String, Object>()
            };

            uniUtils utilsInstance = new uniUtils();
            Object result = utilsInstance.call('populatePicklistCompany', args);

            List<Object> options = (List<Object>) ((Map<String, Object>) args.get('output')).get('options');
            System.assert(options.size() > 0, 'Should include UnipolRental and UnipolTech options');
        }
    }

    @testSetup
    static void setup() {
        // Creazione di un utente di test
        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        Account a = new Account();
        User u = new User(
            Alias = 'testuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = '<EMAIL>',
            FiscalCode__c = '**********',
            IdAzienda__c = a.Id
        );
        insert u;

        // Creazione di un NetworkUser di test
        NetworkUser__c nu = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_1',
            User__c = u.Id
        );
        insert nu;
        NetworkUser__c nu2 = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_4',
            User__c = u.Id
        );
        insert nu2;
        NetworkUser__c nu3 = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_X',
            User__c = u.Id
        );
        insert nu3;
    }

    @isTest
    static void testSocietyPicklistAdmin() {
        // Impostazione dell'utente come amministratore di sistema
        User u = [SELECT Id FROM User WHERE Alias = 'testuser'];
        System.runAs(u) {
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();

            uniUtils util = new uniUtils();
            util.societyPicklist(inputMap, outMap, options);

            List<Map<String, String>> optionsList = (List<Map<String, String>>)outMap.get('options');
            //System.assertEquals(2, optionsList.size());
            //System.assertEquals('unipolsai', optionsList[0].get('name'));
            //System.assertEquals('Unipol', optionsList[0].get('value'));
            //System.assertEquals('unisalute', optionsList[1].get('name'));
            //System.assertEquals('Unisalute', optionsList[1].get('value'));
        }
    }

    @isTest
    static void testSocietyPicklistNonAdmin() {
        // Impostazione dell'utente come non amministratore di sistema
        Profile p = [SELECT Id FROM Profile WHERE Name != 'System Administrator' LIMIT 1];
        Account a = new Account();
        User u = new User(
            Alias = 'testuse2',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'NonAdmin',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = '<EMAIL>',
            FiscalCode__c = '**********',
            IdAzienda__c =  a.Id
        );
        insert u;

        System.runAs(u) {
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();

            uniUtils util = new uniUtils();
            util.invokeMethod('societyPicklist',inputMap, outMap, options);

            List<Map<String, String>> optionsList = (List<Map<String, String>>)outMap.get('options');
            //System.assertEquals(2, optionsList.size());
            //System.assertEquals('unipolsai', optionsList[0].get('name'));
            //System.assertEquals('Unipol', optionsList[0].get('value'));
        }
    }    
}