<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardId</name>
                    <value>RATTATIVE_responsabile_with_table</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableNotifications</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableSubscriptions</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>395</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLinksInNewWindow</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLocation</name>
                    <value>OPEN_IN_STUDIO</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showHeader</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showPostToSlack</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showSharing</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showTitle</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>wave:waveDashboard</componentName>
                <identifier>wave_waveDashboard2</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$User.Profile.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>System Administrator</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.RuoloScrivania}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardId</name>
                    <value>TRATTATIVE_operatori</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableNotifications</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableSubscriptions</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>395</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLinksInNewWindow</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLocation</name>
                    <value>OPEN_IN_STUDIO</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showHeader</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showPostToSlack</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showSharing</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showTitle</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>wave:waveDashboard</componentName>
                <identifier>wave_waveDashboard3</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$User.Profile.Name}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>System Administrator</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.RuoloScrivania}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_CampagneDaLavorare</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.ResponsabileAgenda}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_CampagneAttiveBox</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.ResponsabileAgenda}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_CampagneAccettazioneAttive</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard6</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.ResponsabileAgenda}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardId</name>
                    <value>ATTIVITA_responsabile_with_table</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableNotifications</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableSubscriptions</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>395</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLinksInNewWindow</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLocation</name>
                    <value>OPEN_IN_STUDIO</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showHeader</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showPostToSlack</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showSharing</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showTitle</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>wave:waveDashboard</componentName>
                <identifier>wave_waveDashboard</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$User.Profile.Name}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>System Administrator</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.RuoloScrivania}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardId</name>
                    <value>ATTIVITA_operatori_with_table</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableNotifications</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableSubscriptions</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>395</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLinksInNewWindow</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>openLocation</name>
                    <value>OPEN_IN_STUDIO</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showHeader</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showPostToSlack</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showSharing</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showTitle</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>wave:waveDashboard</componentName>
                <identifier>wave_waveDashboard4</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!$User.Profile.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>System Administrator</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.RuoloScrivania}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>top</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>bottomLeft</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>bottomRight</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_Prodotti</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_Strumenti</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>enableInlineEdit</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>entityName</name>
                    <value>Event</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>filterName</name>
                    <value>Week_Events</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideSearchBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>pageSize</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>flexipage:filterListCard</componentName>
                <identifier>flexipage_filterListCard</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>SC_A_News</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>recordId</name>
                    <value>{recordId}</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard5</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Home_SA</masterLabel>
    <template>
        <name>home:desktopTemplate</name>
    </template>
    <type>HomePage</type>
</FlexiPage>
