<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MasterLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DeveloperName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>BodyType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Endpoint__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>IntegrationId__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Method__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NamedCredential__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Header__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Protocol__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>TimeoutMs__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Version__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PublishLog__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Log_Lifetime__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsProtected</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NamespacePrefix</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9X00000ATlpH</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
