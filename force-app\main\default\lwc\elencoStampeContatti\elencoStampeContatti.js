import { LightningElement, api, track } from 'lwc';
import getDocumentaleLogStampa from "@salesforce/apex/FeaController.getDocumentaleLogStampa";
import getStampe from "@salesforce/apex/FeaController.getStampe";
import getContatti from "@salesforce/apex/FeaController.getContatti";
import Utils from 'c/utils';

export default class ElencoStampeContatti extends LightningElement {

    @api processType;
    @api ciu;
    @api compagnia;
    @api setInit;

    erroreDownload = false;

    @track
    stampe = {
        isOpen: false,
        rows: [],
        columns: [
            { label: 'Operazione', fieldName: 'operazione', hideDefaultActions: true },
            { label: 'Utente', fieldName: 'user', hideDefaultActions: true },
            { label: 'Data Operazione', fieldName: 'dataCreazione', hideDefaultActions: true }, //eliminato , type: 'date'
            { label: 'Data Stampa/Validità', fieldName: 'dataAggiornamento', hideDefaultActions: true }, //eliminato , type: 'date'
            {
                label: '',
                type: 'button-icon',
                fixedWidth: 40,
                typeAttributes: {
                    iconName: 'utility:download',
                    alternativeText: 'Download',
                    title: 'Scarica',
                    variant: 'bare',
                    class: 'download-button',
                    name: 'download' // identificativo per gestirlo nell'evento
                }
            }
        ]
    };

    @track
    contatti = {
        isOpen: false,
        rows: [],
        columns: [
            { label: 'Data registr.', fieldName: 'dataCreazione', hideDefaultActions: true }, //eliminato , type: 'date'
            { label: 'Fonte', fieldName: 'applicazioneChiamante', hideDefaultActions: true },
            { label: 'Tipo', fieldName: 'tipoContatto', hideDefaultActions: true },
            { label: 'Stato', fieldName: 'flagAttivo', hideDefaultActions: true }, //eliminato , type: 'boolean' 
            { label: 'Cellulare', fieldName: 'cellulare', hideDefaultActions: true },
            { label: 'Email', fieldName: 'email', hideDefaultActions: true },
            { label: 'Utente', fieldName: 'utente', hideDefaultActions: true }
        ]
    };

    formattaData(dataInput) {
        if (!dataInput) {
            return '';
        }
        const data = new Date(dataInput);
        if (isNaN(data)) {
            return '';
        }

        const giorno = String(data.getDate()).padStart(2, '0');
        const mese = String(data.getMonth() + 1).padStart(2, '0');
        const anno = data.getFullYear();
        return `${giorno}/${mese}/${anno}`;
    }

    connectedCallback(){
        //AC: come spiegato in testa, questo intervallo è necessario per verificare che gli attributi vengano passati dalla flex card.
        //    per evitare che il ciclo venga eseguito all'infinito in caso di mancata assegnazione di setInit, imposto un contatore.
        //    il contatore arriva fino a 20 (per un totale di 2 secondi), se non viene inizializzato, interrompe l'intervallo e mosta un messaggio di errore.
        let cnt = 0;
        const ci = setInterval(() => {
            if (this.setInit) {
                this.init();
                clearInterval(ci);
            } else {
                cnt++;
                if (cnt > 20) {
                    clearInterval(ci);
                    this.error("errore inzializzazione");
                }
            }
        }, 100);
    }

    init(){
        console.log('>>> elencoStampeContatti ciu: ' + this.ciu);
        console.log('>>> elencoStampeContatti compagnia: ' + this.compagnia);
    }

    //AC: preleva l'id in base all'elemento html che viene cliccato
    getId(event) {
        return event.target.dataset.id ?? event.target.parentElement.dataset.id;
    }

    //AC: gestisce la visualizzazione del pannello con le tabelle
    toggleSection(event) {
        const id = this.getId(event);
        this[id].isOpen = !this[id].isOpen;
        const handle = Utils.capitalize(id);
        this[`handle${handle}`]();
    }

    //AC: azione legata al pulsante download
    handleRowAction(event) {
        const actionName = event.detail.action.name;
        if (actionName === 'download') {
            this.handleDownload(event.detail.row);
        }
    }

    handleDownload(data) {
        this.refs.loader.showLoader();
        getDocumentaleLogStampa({
            ipInput: {
                ciu: data.ciu,
                tipoDocAnagrafica: data.tipoDocumento,
                idDocAnagrafica: data.id
            }
        }).then(response => {
            erroreDownload = false;
            const byteCharacters = atob(response.returnList.base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'application/pdf' });
            const blobUrl = URL.createObjectURL(blob);
            window.open(blobUrl, '_blank');
        }).catch(err => {
            this.error(err);
            erroreDownload = true;
        }).finally(() => this.refs.loader.hideLoader());
    }

    //AC: invoca il servizio che preleva le stampe
    handleStampe() {
        if (this.stampe.isOpen && this.stampe.rows.length === 0) {
            this.refs.loader.showLoader();
            getStampe({
                ipInput: {
                    ciu: this.ciu.toString(),
                    compagnia: this.compagnia.toLowerCase(),
                    processType: this.processType.toUpperCase()
                }
            }).then(response => {
                this.stampe.rows = response.returnList.results;
                this.stampe.rows.forEach(obj => {
                    obj.user = obj.datiTracciatura.usernameCreazione ?? obj.datiTracciatura.usernameUltimoAggiornamento;
                    obj.operazione = obj.datiTracciatura.tipoDocumento === 'AFEA' ? "Adesione" : "Adeguamento Edizione";
                    obj.dataCreazione = this.formattaData(obj.datiTracciatura.dataCreazione);
                    obj.dataAggiornamento = this.formattaData(obj.datiTracciatura.dataUltimoAggiornamento);
                });
            }).catch(err => {
                this.error(err);
            }).finally(() => this.refs.loader.hideLoader());
        }
    }

    //AC: invoca il servizio che preleva i contatti
    handleContatti() {
        if (this.contatti.isOpen & this.contatti.rows.length === 0) {
            this.refs.loader.showLoader();
            getContatti({
                ipInput: {
                    ciu: this.ciu.toString(),
                    compagnia: this.compagnia.toLowerCase(),
                    mostraDatiTracciatura: "TRUE",
                    contactTypeFilter: this.processType.toUpperCase()
                }
            }).then(response => {
                this.contatti.rows = Array.isArray(response.returnList) ? response.returnList : [response.returnList];
                this.contatti.rows.forEach(obj => {
                    obj.dataCreazione = this.formattaData(obj.datiTracciatura.dataCreazione);
                    obj.applicazioneChiamante = obj.datiTracciatura.proceduraChiamante;
                    obj.utente = obj.datiTracciatura.userIdUltimoAggiornamento + ' ' + obj.datiTracciatura.usernameUltimoAggiornamento;
                    obj.flagAttivo === true ? obj.flagAttivo = 'Attivo' : 'Disattivo';
                    obj.tipoContatto = obj.tipoContatto === 'D' ? obj.tipoContatto = 'Dispositivo' : 'Aggiuntivo';
                    //obj.userId = obj.datiTracciatura.userIdCreazione;
                });
            }).catch(err => {
                this.error(err);
            }).finally(() => this.refs.loader.hideLoader());
        }
    }

    error(err) {
        console.log(err);
        //this.errorInit = true;
    }

    //AC: la scelta di usare le proprietà è legata al fatto che la visibilità dovrebbe essere soggetta a logiche
    //    proprietà che gestiscono la visibilità delle icone
    get iconNameStampe() {
        return this.stampe.isOpen ? 'utility:chevrondown' : 'utility:chevronright';
    }
    get iconNameContatti() {
        return this.contatti.isOpen ? 'utility:chevrondown' : 'utility:chevronright';
    }
}