import { LightningElement, api, wire, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import pubsub from "omnistudio/pubsub";
import getWrapper from '@salesforce/apex/CtrlFeiContainer.getWrapper';
import callIntegrationProcedure from '@salesforce/apex/CtrlFeiContainer.callIntegrationProcedure';
import { CloseActionScreenEvent } from 'lightning/actions';

export default class FeiContainer extends NavigationMixin(LightningElement) {

    @track feistep = 0;

    @api recordId;
    @api FEIID;
    @api FiscalCode;
    @api feiRequestPayload;
    @api permission;
    @api permissionSetName;
    @api feiAddressEndpointPayload;
    @api cfcontid;
    @api UserId;
    @api society;

    @track spinnerSingle = false;

    @track userError;

    @track checked = true;

    prefillData = "";

    @track options = [];
    @track selectedValue;

    get showSelect(){
        return this.options != null && this.options.length > 1;
    }

    get showError100(){
        return (this.userError == "ERR-100");
    }

    get showError200(){
        return (this.userError == "ERR-200");
    }

    get showDefault(){
        return (this.society != null && this.society != undefined && this.society != "" && this.society != "null");
    }

    @api
    set feiid(value){
        if(value != null && value != undefined && value != "" && (this.FEIID == undefined || this.FEIID == null || this.FEIID == "")){
            this.FEIID = value;
        }
    }

    get feiid(){
        return this.FEIID;
    }

    @api
    set fiscalcode(value){
        if(value != null && value != undefined && value != "" && (this.FiscalCode == undefined || this.FiscalCode == null || this.FiscalCode == "")){
            this.FiscalCode = value;
        }
    }

    get fiscalcode(){
        return this.FiscalCode;
    }

    @track showExtensionMessage = false;
    @track tabCount;
    @track isLoaded = false;

    @track allowContinue = false;
    
    @track feiSettings = {};

    get nextDisabled(){
        return ((this.feiSettings != null && this.feiSettings != undefined) ? this.feiSettings.extensionRequired : false);
    }

    @track browserName;
    
    @track feiURL;

    get extensionURL(){

        let currentValue;

        if(this.feiSettings != null && this.feiSettings != undefined){

            if(this.browserName == "Safari"){
                currentValue = this.feiSettings.safariExtensionURL;
            }else{
                currentValue = this.feiSettings.chromeExtensionURL;
            }

        }

        return currentValue;

    }

    get showStepZero(){
        return (this.feistep == 0);
    }

    get showStepOne(){
        return (this.feistep == 1);
    }

    get showStepTwo(){
        return (this.feistep == 2);
    }

    connectedCallback(){

        this.feistep = 0;

        console.log('FEIID --> ',this.FEIID);
        console.log('FiscalCode --> ',this.FiscalCode);
        console.log('recordId --> ',this.recordId);
        console.log('feiRequestPayload --> ',this.feiRequestPayload);
        console.log('permissionSetName --> ',this.permissionSetName);
        console.log('cfcontid --> ',this.cfcontid);
        
        this.permission = this.permissionSetName;

        window.addEventListener("message", evt => {

            console.log('event ok feiContainer ok');

            this.isLoaded = false;

            if(evt.data.type == "feiFlowFinished"){

                pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
                this.dispatchEvent(new CloseActionScreenEvent());
            }

            if(evt.data.type == "CHECK_TABS"){

                this.showExtensionMessage = true;
                this.allowContinue = false;

            }else if(evt.data.type == "TAB_COUNT_RESPONSE"){
                this.tabCount = evt.data.count;
                this.allowContinue = true;
                this.showExtensionMessage = false;
               
                if(this.tabCount != undefined && this.tabCount == 0){
                    //this.omniNextStep();
                }else{

                    //this.goNext();
                    
                }

            }

            this.isLoaded = true;

        });

        const userAgent = navigator.userAgent;

        if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') === -1 && userAgent.indexOf('OPR') === -1) {
            this.browserName = 'Chrome';
        } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
            this.browserName = 'Safari';
        } else if (userAgent.indexOf('Firefox') > -1) {
            this.browserName = 'Firefox';
        } else if (userAgent.indexOf('Edge') > -1) {
            this.browserName = 'Edge';
        } else if (userAgent.indexOf('OPR') > -1 || userAgent.indexOf('Opera') > -1) {
            this.browserName = 'Opera';
        } else if (userAgent.indexOf('Trident') > -1 || userAgent.indexOf('MSIE') > -1) {
            this.browserName = 'Internet Explorer';
        } else {
            this.browserName = 'Unknown';
        }

        console.log(`Browser detected: ${this.browserName}`);

        this.getTabCount();

        getWrapper({ permissionSetName : this.permissionSetName, society: this.society })
            .then((result) => {

                this.feiSettings = result.extension;

                console.log('res users --> ',result.users);

                if(result.users != undefined && result.users != null && result.users != ""){

                    if(result.users.length == 1 && result.users[0].value == "ERR-100"){

                        this.userError = "ERR-100";

                    }else if(result.users.length == 1 && result.users[0].value == "ERR-200"){

                        this.userError = "ERR-200";

                    }else{
                    
                        let res = [];

                        for(let i in result.users){

                            let tempObj = {};
                            tempObj.label = result.users[i].label;
                            tempObj.value = result.users[i].value;

                            res.push(tempObj);

                        }

                        console.log('res option --> ',res);

                        this.options = res;

                        if(this.options != null && this.options.length > 0 && this.options.length == 2){

                            let currentOption = this.options[1];

                            let newRequestPayload;

                            if(this.feiRequestPayload != null && this.feiRequestPayload != undefined && this.feiRequestPayload != ""){

                                let tempRequest = JSON.stringify(this.feiRequestPayload);

                                if(tempRequest.includes("{$agenzia}")){
                                    tempRequest = tempRequest.replaceAll("{$agenzia}",currentOption.value.split("$$")[1]);
                                }

                                if(tempRequest.includes("{$compagnia}")){
                                    tempRequest = tempRequest.replaceAll("{$compagnia}",currentOption.value.split("$$")[2]);
                                }

                                newRequestPayload = JSON.parse(tempRequest);

                            }else{
                                newRequestPayload = this.feiRequestPayload;
                            }

                            console.log('nrp --> '+newRequestPayload);

                            this.feiRequestPayload = newRequestPayload;
                            this.UserID = currentOption.value.split("$$")[0];

                            this.goNextZero();

                        }
                    }

                }

            })
            .catch((error) => {
                console.error(error);
                this.isLoaded = true;
            })
            .finally(() => {
                this.isLoaded = true;
            })

    }

    closeTabs(){
        window.postMessage({ type: "CLOSE_TABS", usage: "internal" }, "*");
    }

    getTabCount(){
        const message = { type: 'CHECK_TABS' };
        console.log('Invio messaggio CHECK_TABS via window.postMessage');
        window.postMessage(message, '*');
    }

    handleToggle(event){
        this.checked = !this.checked;
    }

    handleChange(event){

        console.log('',event);
        console.log('value --> ',event.detail.value);

        let currentOption = event.detail.value;

        let newRequestPayload;

        if(this.feiRequestPayload != null && this.feiRequestPayload != undefined && this.feiRequestPayload != ""){

            let tempRequest = JSON.stringify(this.feiRequestPayload);

            console.log('tr --> '+tempRequest);

            if(tempRequest.includes("{$agenzia}")){
                tempRequest = tempRequest.replaceAll("{$agenzia}",currentOption.split("$$")[1]);
            }

            if(tempRequest.includes("{$compagnia}")){
                tempRequest = tempRequest.replaceAll("{$compagnia}",currentOption.split("$$")[2]);
            }

            newRequestPayload = JSON.parse(tempRequest);

        }else{
            newRequestPayload = this.feiRequestPayload;
        }

        this.feiRequestPayload = newRequestPayload;
        this.UserID = currentOption.split("$$")[0];

        console.log('nrp pre --> '+this.feiRequestPayload);
        console.log('nrp --> '+newRequestPayload);

    }

    goBackZero(){
        // close ?
    }

    goNextZero(){

        if(this.UserID == null || this.UserID == undefined || this.UserID == ""){

        }else{

            if(this.feiSettings.skipExtension){
                this.feistep = 2;
                this.spinnerSingle = true;
                this.goNextWithIP();
            }else{
                this.feistep = 1;
            }

        }

    }

    goToCruscotto(){

        /*this[NavigationMixin.Navigate]({
            type: 'standard__navItemPage',
            attributes: {
                apiName: 'Gruppi_di_Utenti' // <-- sostituisci con il tuo API Name del tab
            }
        });*/

        location.href = "/lightning/n/CruscottoDefaultUser";

    }

    goBackOne(){

        this.feistep = 0;

    }

    goNextOne(){
        this.feistep = 2;
        this.goNextWithIP();
    }

    goNextWithIP(){

        console.log('frp --> ',this.feiRequestPayload);

        this.isLoaded = false;
        this.spinnerSingle = true;

        console.log('defaultChecked --> ',this.checked);

        callIntegrationProcedure({ 
            "FEIID": this.FEIID,
            "feiRequestPayload": this.feiRequestPayload,
            "recordId": this.recordId,
            "UserID": this.UserID,
            "feiAddressEndpointPayload": this.feiAddressEndpointPayload,
            "cfcontid": this.cfcontid,
            "defaultChecked": this.checked,
            "society": this.society
        })
            .then((result) => {

                console.log(result);
                this.feiURL = result;

                try{

                    this.closeTabs();

                    setTimeout(()=>{
                        window.parent.postMessage({ type: "feiFlowFinished" }, "*");
                        window.postMessage({ type: "feiFlowFinished" }, "*");
                    },500);
                    
                    window.open(this.feiURL, "leonardoTab");
                    
                }catch(ex){
                    console.error(ex);
                }

            })
            .catch((error) => {
                console.error(error);
            })
            .finally(() => {
                this.isLoaded = true;
                this.spinnerSingle = false;
            })

    }

}