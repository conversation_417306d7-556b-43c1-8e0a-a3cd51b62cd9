:host {
    --slds-c-icon-color-foreground: #0070EF;
}

.root {
    list-style: none;
    margin: 0;
    padding: 0;
}

.item {
    position: relative;     
}

.parent {
    display: flex;
    align-items: center;
    cursor: pointer;
    text-decoration: none;
    color: var(--lwc-colorTextDefault, #080707);
    padding: 0.5rem 0.25rem;
}

.item > .item-leaf {
    align-items: center;
    text-decoration: none;
    color: var(--lwc-colorTextDefault, #080707);
    padding: 0.5rem 0.25rem;
}

.item > .item-leaf > a {
    cursor: pointer;
}

.favorite-colored {
    cursor: pointer;
    --slds-c-icon-color-foreground: #0070EF;
}

.favorite {
    cursor: pointer;
}

.item > a:hover, .parent:hover {
    background: var(--lwc-colorBackgroundAlt, #f3f2f2);
}

.chevron { margin-left: auto; }

.popup {
    position: absolute;
    top: -1px;                      
    left: 100%;
    min-width: 240px;
    background: #fff;
    border: 1px solid var(--lwc-borderColor, #dddbda);
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    z-index: 10;
    list-style: none;
    margin: 0;
    padding: 0;
}

.padding-left_5 {
    padding-left: 5px;
}

.popup.flip {
  left: auto;
  right: 100%;
}