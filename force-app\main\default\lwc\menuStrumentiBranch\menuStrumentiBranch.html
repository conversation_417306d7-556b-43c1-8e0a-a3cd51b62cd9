<template>
    <ul class="root">
        <template for:each={items} for:item="itm">
            <li key={itm.developerName} class="item">
                <template if:true={itm.isLeaf}>
                    <div class="item-leaf">
                        <a href="#" data-id={itm.developerName} onclick={handleLeafClick}>
                            <span>{itm.label}</span>
                            <lightning-icon icon-name="utility:new_window" size="x-small" class="chevron padding-left_5"></lightning-icon>
                        </a>
                        <template lwc:if={itm.isFavorite}>
                            <lightning-icon icon-name="utility:favorite" size="x-small" class="chevron favorite-colored padding-left_5" data-id={itm.developerName} onclick={handleUnfavoriteClick}></lightning-icon>
                        </template>
                        <template lwc:else>
                            <lightning-icon icon-name="utility:favorite_alt" size="x-small" class="chevron favorite padding-left_5" data-id={itm.developerName} onclick={handleFavoriteClick}></lightning-icon>
                        </template>
                    </div>
                </template>

                <template if:false={itm.isLeaf}>
                    <div class="parent" onclick={toggleBranch} data-id={itm.developerName}>
                        <span>{itm.label}</span>
                        <lightning-icon icon-name="utility:chevronright" size="x-small" class="chevron">
                        </lightning-icon>
                    </div>

                    <template if:true={itm.expanded}>
                        <ul class="popup">
                            <c-menu-strumenti-branch items={itm.children}></c-menu-strumenti-branch>
                        </ul>
                    </template>
                </template>
            </li>
        </template>
    </ul>
</template>