import { LightningElement, wire, track} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';

import apexGetTitoliInScadenzaFromIntegrationProcedure from '@salesforce/apex/TitoliFeiPermissionHelper.getTitoliInScadenzaFromIntegrationProcedure';
import apexGetUserMandati from '@salesforce/apex/TitoliFeiPermissionHelper.getUserMandati';
import apexGetParamsForFei from '@salesforce/apex/TitoliFeiPermissionHelper.getParamsForFei';
import apexEnableActionsAsNeeded from '@salesforce/apex/TitoliFeiPermissionHelper.enableActionsAsNeeded';
import apexGetPrt from '@salesforce/apex/TitoliFeiPermissionHelper.getPrt';
//import apexGetAccountDetails from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountDetails';
//import apexCreateBarcodeAndXML from '@salesforce/apex/TitoliFeiPermissionHelper.createBarcodeAndXML';

const actions = [
    { label: 'Completa', name: 'completa', disabled: false },
    { label: 'Incassa', name: 'incassa', disabled: false },
    { label: 'FEA', name: 'fea', disabled: true },
    { label: 'Firma', name: 'firma', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: true },
    //{ label: 'Vedi Documento', name: 'vediDocumento', disabled: true },
    //{ label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: true },
    //{ label: 'Incasso', name: 'Incasso', disabled: true },
    //{ label: 'Invio da remoto', name: 'invioDaremoto', disabled: true },
    { label: 'Stampa', name: 'stampa', disabled: false },
    //{ label: 'Sostituzione', name: 'sostituzione', disabled: true },
    //{ label: 'Variazione', name: 'variazione', disabled: true },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false },
    //{ label: 'Aggiungi ai preferiti', name: 'aggiungiAiPreferiti', disabled: true }
];

export default class TitoliInScadenzaCmp extends OmniscriptBaseMixin(LightningElement) {

    actions = actions;
    titoliInScadenzaColumn = [];
    titoliInScadenzaData = [];
    datatableRendered = false;
    isFlowModalOpened = false;
    isLWCModalOpened = false;

    params = {
        feiId: "feiId",
        fiscalcode: "CF",
        feiRequestPayload: "{}",
        permissionSetName: "permissionSetName"
    }

    @wire(CurrentPageReference)
    currentPageReference;

    connectedCallback() {  
        this.accountId = this.currentPageReference.state.c__AccountId;
        this.getTitoliInScadenzaData();
        this.getUserMandati();
    }

    getTitoliInScadenzaData(){
        apexGetTitoliInScadenzaFromIntegrationProcedure({accountId : this.accountId})
        .then((result) => {
            this.titoliInScadenzaData = Array.isArray(result['Response']) ? result['Response'] : [result['Response']];
            console.log('titoliInScadenzaDataResult: '+JSON.stringify(this.titoliInScadenzaData));
            this.enableActionsAsNeeded();
        })
        .catch((error) => {
            console.log(JSON.stringify(error));
            //TODO: insert show toast
        });
    }

    getUserMandati(){
        apexGetUserMandati({accId: this.accountId})
        .then((result) => {
            this.showUnipolButton = result['hasMandatoUnipol']; //TODO: aggiungi custom permission
            this.showUniSaluteButton = result['hasMandatoUnisalute'];
        })
        .catch((error) => {
            //TODO: insert show toast
            console.log(JSON.stringify(error));
        });
    }

    getRowActions(row, doneCallback) {
        const matchingRow = this.titoliInScadenzaData.find(r => r.rowId === row.rowId);
        const actions = matchingRow?.rowActions || [];
        doneCallback(actions);
    }

    enableActionsAsNeeded(){
        this.datatableRendered = false;
        apexEnableActionsAsNeeded({actionsJSON: JSON.stringify(this.actions)})
        .then((result) => {
            let arrayTitoli = [];
            let rows = [...this.titoliInScadenzaData];
            this.titoliInScadenzaData = [];
            rows.forEach(titolo => {

                let feaPresent = true;
                let feaAction = {};
                let firmaAction = {};
                let currentRow = { ...titolo };
                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                }
                currentRow.rowActions = result.map(action => ({ ...action }));
                console.log('check foreach: ' + JSON.stringify(currentRow));

                currentRow.rowActions.forEach(action => {


                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'fea':
                            
                            if (currentRow?.tipo !== 'Quietanza') {
                                (console.log('fea removed'));
                                //currentRow.rowActions.splice(currentRow.rowActions.indexOf(action), 1);
                                //action.hidden = true;
                                feaAction = action;
                                feaPresent = false;
                            } else {

                                if (currentRow?.isStampabileInFEA === false || currentRow?.prodottoAbilitatoInvioFea === false || currentRow?.inviiRemoti !== '0' || currentRow.inviiAr !== '0')  {
                                    console.log('entro check: ' + action.name);
                                    action.disabled = true;
                                }
                            }
                                    
                            break;
                        
                        case 'completa':
                            
                            if (currentRow?.operazione === 'R' || (currentRow?.firmata === 'Firmato' && (currentRow?.emessaNonIncassabile === false || (currentRow?.frazionamento !== undefined && currentRow?.frazionamento.toLowerCase() !== 'annuale'))) || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)){
                                console.log('entro check: ' + action.name);
                                action.disabled = true;
                            }
                            break;
                    
                        case 'incassa':
                            
                            if(currentRow?.operazione === 'R' || currentRow?.firmata === 'Non firmato' || currentRow?.emessaNonIncassabile === true){
                                console.log('entro check: ' + action.name);
                                action.disabled = true;
                            }
                            break;

                        case 'rettifica':
                            
                            if (currentRow?.operazione === 'I') {
                                console.log('entro check: ' + action.name);
                                action.disabled = true;
                            }
                            break;
                        
                        case 'firma':
                            if (feaPresent === true) {
                                (console.log('firma removed'));
                                //currentRow.rowActions.splice(currentRow.rowActions.indexOf(action), 1);
                                firmaAction = action;
                            } else {
                                if (currentRow?.firmata === 'Firmato' || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)) {
                                    console.log('entro check: ' + action.name);
                                    action.disabled = true;
                                }
                            }
                            break;

                        case 'schedaQT':
                                
                            if (currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) {
                                console.log('entro check: ' + action.name);
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                if (feaPresent == false) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(feaAction), 1);
                } else if (feaPresent == true) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(firmaAction), 1);
                }

                arrayTitoli.push(currentRow);
            });
            console.log('titoliInScadenzaData post lavorazione: ' + JSON.stringify(arrayTitoli));
            this.titoliInScadenzaData = [...arrayTitoli];
            this.titoliInScadenzaColumn = [
                { label: 'Società', fieldName: 'compagnia' },
                { label: 'Tipo', fieldName: 'tipo' },
                { label: 'Ambito', fieldName: 'ambito' },
                { label: 'N. Folder', fieldName: 'nFolder' },
                { label: 'N. Polizza/Posizione', fieldName: 'polizza' },
                { label: 'Scadenza', fieldName: 'dataScadenzaTitolo' },
                { label: 'Frazionamento', fieldName: 'frazionamento' },
                { label: 'Premio (comprensivo di Unibox)', fieldName: 'premio', type: 'currency' },
                { label: 'di cui Unibox', fieldName: 'unibox' },
                { label: 'Omnicanalità', fieldName: 'omnicanalita' },
                { label: 'Esposizione AR', fieldName: 'esposizioneAR', type: 'boolean', cellAttributes: { alignment: 'center' }},
                { label: 'Targa', fieldName: 'targa' },
                { label: 'Modello', fieldName: 'modello' },
                { label: 'Titoli al legale', fieldName: 'titoliAlLegale' },
                { label: 'Rettificabile', fieldName: 'rettificabile', type: 'boolean', cellAttributes: { alignment: 'center' }},
                { label: 'Firmato', fieldName: 'firmata' },
                { type: 'action', typeAttributes: { rowActions: this.getRowActions.bind(this), menuAlignment: 'auto'} }
            ];
            this.datatableRendered = true;
        })
        .catch((error) => {
            console.log(JSON.stringify(error));
            //TODO: insert show toast
        });
    }

    handleStampe(event){
        let feiId = event.currentTarget.dataset.feiId;
        this.flowInputs = [
            { name: 'FEIID', type: 'String', value: feiId},
            { name: 'recordId', type: 'String', value: this.accountId }              
        ];
        this.toggleFlowModal();
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }

    toggleLWCModal(){
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
           this.toggleFlowModal();
    }

    handleRowAction(event){
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        console.log("actionName: " + actionName);
        switch (actionName) {
            case 'completa': {
                let feiId = 'PU.COMPLETA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'incassa': {
                let feiId = 'CP.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({agencyCode: '$agenzia', companyCode: '$compagnia', folderId: row.nFolder}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'firma': {
                let feiId = 'PU.FIRMA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'rettifica': {
                let feiId = 'CP.RETTIFICA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({agencyCode: '$agenzia', companyCode: '$compagnia', folderId: row.nFolder}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'schedaQT': {
                let feiId = 'SCHEDA.QT';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({xqt: row.xquietanzaId, agency: '$agenzia'}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }

            case 'stampa': {
                let stampaParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                stampaParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                console.log('uuid: '+uuid+'\n');
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                stampaParams.body = JSON.stringify({
                    "filtroStampaQtzListaDocumentiDaStampare": {
                        "ristampa": true,
                        "fromFe": true,
                        "motRistampaTpCd": "-1",
                        "testoMotRistampa": "Stampa precedente rovinata",
                        "flagAnteprima": false,
                        "flagGeneraDocumenti": false,
                        "listaDocumentiDaStampare": [
                            {
                                "idQt": row?.uplCrmQuietId,
                                "tipoDocumento": tipoDocumento,
                                "idDocumento": row?.idDoc
                            }
                        ],
                        "docTpCd": row?.tipoDocumento,
                        "guid": uuid,
                        "agenziaFiglia": row?.agenzia,
                        "agenziaMadre": row?.agenziaMadre,
                        "compagnia": row?.compagnia
                    }
                });
                console.log(`stampaParams: ${JSON.stringify(stampaParams)}`);
                apexGetPrt({inputParams: stampaParams})
                .then(resultPrt => {

                    //continua qui
                    const feiId = 'SFEA.PRINT.PAPER';
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify(resultPrt), //TODO: metti json completo
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        };
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    });
                }).catch(error => {
                        console.error('Error retrieving PRT:', error);
                })

                break;
            }

            case 'fea': {
                let feaParams = this.getDefaultFEAParams(row);
                feaParams.prtForPrint = false;

                /*
                apexGetAccountDetails({accountId: this.accountId, societa: row.societa})
                .then(result => {
                    let accountDetailsMap = {};
                    if (result.length > 0) {
                        
                        let accountDetails = result[0];
                        accountDetailsMap.ciu = accountDetails.SourceSystemIdentifier__c;
                        accountDetailsMap.mobile = accountDetails.Mobile__c;
                        accountDetailsMap.email = accountDetails.Email__c;
                        accountDetailsMap.type = 'FEA' ;
                    }

                    apexCreateBarcodeAndXML({inputMap: accountDetailsMap})
                    .then(result => {
                        console.log('barcode And XML:', JSON.stringify(result));
                        //continua qui
                        let jsonOutput = result['feiRequest'];
                    */
                        apexGetPrt({inputParams: feaParams}).then(result => {
                            console.log('PRT retrieved successfully:', result);
                            this.executeFeaFei(result);
                        }).catch(error => {
                                console.error('Error retrieving PRT:', error);
                        })
                    /*
                    })
                

                })
                    */
                break;
            }

            default:
                console.warn('Azione non riconosciuta:', actionName);
                this.params = null;
        }
    }

    executeFeaFei(params, ){
        if (params.FEI == 'requestSign'){
            let feiId = 'SFEA.SIGN';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(params), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || ""
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
            return;
        }
        if (params.FEI == 'resumeSign'){
            let feiId = 'SFEA.SIGN.DASHBOARD';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(params), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || ""
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
        }
    }

    getDefaultFEAParams(row) {
        return {
            idDoc: row.idDoc,
            ramo: row.ramo,
            polizza: row.polizza,
            polizzaProdottoUnico: row.isProdottoUnico,
            numeroAppendice: row.numeroAppendice,
            dataEffettoTitolo: row.dataEffettoTitolo
        };

    }
}