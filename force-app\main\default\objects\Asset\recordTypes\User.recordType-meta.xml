<?xml version="1.0" encoding="UTF-8"?>
<RecordType xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>User</fullName>
    <active>true</active>
    <label>User</label>
    <picklistValues>
        <picklist>Category__c</picklist>
        <values>
            <fullName>Preferiti</fullName>
            <default>true</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>ConsequenceOfFailure</picklist>
        <values>
            <fullName>Critical</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Insignificant</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Major</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Minor</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Moderate</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>DigitalAssetStatus</picklist>
        <values>
            <fullName>Error</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Off</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>On</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Warning</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Status</picklist>
        <values>
            <fullName>Installed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Obsolete</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Purchased</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Registered</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Shipped</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>StatusReason</picklist>
        <values>
            <fullName>Not Ready</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Off</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Offline</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Online</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Paused</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Standby</fullName>
            <default>false</default>
        </values>
    </picklistValues>
</RecordType>
