<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedOutputJson>{
  &quot;type&quot; : &quot;Text&quot;,
  &quot;feiId&quot; : &quot;Text&quot;,
  &quot;redirect&quot; : &quot;Text&quot;,
  &quot;redirectType&quot; : &quot;Text&quot;
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TransformPreferitiStructure</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom796</globalKey>
        <inputFieldName>DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>apiName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9584</globalKey>
        <inputFieldName>MasterLabel</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>masterLabel</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom6481</globalKey>
        <inputFieldName>Type__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>type</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9837</globalKey>
        <inputFieldName>FEI_ID__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>feiId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom1757</globalKey>
        <inputFieldName>Redirect_Link__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>redirect</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9270</globalKey>
        <inputFieldName>Redirect_Type__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>redirectType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>[ {
  &quot;attributes&quot; : {
    &quot;type&quot; : &quot;Menu_Strumenti_Tree_Structure__mdt&quot;,
    &quot;url&quot; : &quot;/services/data/v64.0/sobjects/Menu_Strumenti_Tree_Structure__mdt/m0i9O000006USUrQAO&quot;
  },
  &quot;MasterLabel&quot; : &quot;Disponibilità su Canali Digitali&quot;,
  &quot;DeveloperName&quot; : &quot;UN_DISPOCANALDIGI&quot;,
  &quot;Is_Active__c&quot; : true,
  &quot;Context__c&quot; : &quot;MENU_STRUMENTI_UNIPOL&quot;,
  &quot;Section__c&quot; : &quot;UN_STRUMENTI&quot;,
  &quot;Order__c&quot; : 6,
  &quot;Type__c&quot; : &quot;REDIRECT INTERNO&quot;,
  &quot;Redirect__c&quot; : &quot;Disponibilita_Agenda&quot;,
  &quot;Redirect_Type__c&quot; : &quot;standard__navItemPage&quot;,
  &quot;Id&quot; : &quot;m0i9O000006USUrQAO&quot;
}, {
  &quot;attributes&quot; : {
    &quot;type&quot; : &quot;Menu_Strumenti_Tree_Structure__mdt&quot;,
    &quot;url&quot; : &quot;/services/data/v64.0/sobjects/Menu_Strumenti_Tree_Structure__mdt/m0i9O000006Vf3sQAC&quot;
  },
  &quot;MasterLabel&quot; : &quot;Preventivi e Autorizzazioni&quot;,
  &quot;DeveloperName&quot; : &quot;UN_PREVAUTORIZZ&quot;,
  &quot;Is_Active__c&quot; : true,
  &quot;Context__c&quot; : &quot;MENU_STRUMENTI_UNIPOL&quot;,
  &quot;Section__c&quot; : &quot;UN_STRUMENTI&quot;,
  &quot;Order__c&quot; : 3,
  &quot;Type__c&quot; : &quot;FEI&quot;,
  &quot;FEI_ID__c&quot; : &quot;PREVENTIVI.AUTORIZZAZIONI&quot;,
  &quot;Id&quot; : &quot;m0i9O000006Vf3sQAC&quot;
} ]</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TransformPreferitiStructure_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
