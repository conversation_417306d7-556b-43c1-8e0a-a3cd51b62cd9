<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description>DM per estrarre AccDetailsId collegati ad un AccountId, non gestito UnipolMove e UnipolRental</description>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>UniGetAccountDetailsId</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>accountId</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom9838</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccAccRelationUnipol</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccAccRelationUnipol:Id</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom8597</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>UnipolAccountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccountDetailsIdCustom301</globalKey>
        <inputFieldName>UnipolAccountDetails:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unipolAccountDetailsId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccountDetailsIdCustom4036</globalKey>
        <inputFieldName>UnisaluteAccountDetails:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unisaluteAccountDetailsId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;SOC_4&apos;</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom8670</globalKey>
        <inputFieldName>FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccAccRelationUnisalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccountDetailsIdCustom3932</globalKey>
        <inputFieldName>UnipolAccountDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unipolCiu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetAccountDetailsIdCustom3006</globalKey>
        <inputFieldName>UnisaluteAccountDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unisaluteCiu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>accountId</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom3429</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccAccRelationUnisalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;SOC_1&apos;</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom998</globalKey>
        <inputFieldName>FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccAccRelationUnipol</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccAccRelationUnisalute:Id</filterValue>
        <globalKey>UniGetAccountDetailsIdCustom2406</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetAccountDetailsId</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>UnisaluteAccountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;accountId&quot; : &quot;0019X00000tIvmXQAS&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetAccountDetailsId_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
