<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;GetDocumentsDetails&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>UniGetDocumentDetails</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem14</globalKey>
        <inputFieldName>accountDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem15</globalKey>
        <inputFieldName>nomeCompagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:nomeCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem12</globalKey>
        <inputFieldName>contentVersions:ExpirationDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:dataScadenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem13</globalKey>
        <inputFieldName>contentVersions:ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:externalId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem10</globalKey>
        <inputFieldName>contentVersions:IssuingCountryCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:stato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem11</globalKey>
        <inputFieldName>contentVersions:RecordType.DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:tipologiaRT</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| NOW</formulaConverted>
        <formulaExpression>NOW()</formulaExpression>
        <formulaResultPath>timestamp</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom6301</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom784</globalKey>
        <inputFieldName>timestamp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:timestamp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem8</globalKey>
        <inputFieldName>contentVersions:IssuingLocalityCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:comune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem9</globalKey>
        <inputFieldName>contentVersions:IssuingProvince__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:provincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem16</globalKey>
        <inputFieldName>contentVersions:RecordType.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:tipologia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem6</globalKey>
        <inputFieldName>contentVersions:Number__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:numeroDocumento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem7</globalKey>
        <inputFieldName>contentVersions:EffectiveStartDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:dataValidita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Societa &quot;SOC_1&quot; == &quot;unipolsai&quot; | var:Societa &quot;SOC_4&quot; == &quot;unisalute&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Societa == &quot;SOC_1&quot;, &quot;unipolsai&quot;, 
  IF(Societa == &quot;SOC_4&quot;, &quot;unisalute&quot;, &quot;&quot;)
)</formulaExpression>
        <formulaResultPath>nomeCompagnia</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Status</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem5</globalKey>
        <inputFieldName>Status__c</inputFieldName>
        <inputObjectName>ContentVersion</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentVersions</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>contentLinks:ContentDocumentId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem2</globalKey>
        <inputFieldName>ContentDocumentId</inputFieldName>
        <inputObjectName>ContentVersion</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentVersions</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Societa</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem3</globalKey>
        <inputFieldName>Relation__r.FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>accountDetails:Id</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem0</globalKey>
        <inputFieldName>LinkedEntityId</inputFieldName>
        <inputObjectName>ContentDocumentLink</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentLinks</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000tqQzUAIItem1</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X000015e2ZLQAY&quot;,
  &quot;Societa&quot; : &quot;SOC_1&quot;,
  &quot;Status&quot; : &quot;A&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetDocumentDetails_6</uniqueName>
    <versionNumber>6.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
