<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>UniGetNetworkUserInfo</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem21</globalKey>
        <inputFieldName>User:Profile.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>profileName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem19</globalKey>
        <inputFieldName>Mandato:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>codiceAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem20</globalKey>
        <inputFieldName>username</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>username</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem17</globalKey>
        <inputFieldName>compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem18</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>companyId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem6</globalKey>
        <inputFieldName>id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;TRUE&apos;</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem7</globalKey>
        <inputFieldName>IsActive__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:AccountDetail:SourceSystemIdentifier__c</formulaConverted>
        <formulaExpression>AccountDetail:SourceSystemIdentifier__c</formulaExpression>
        <formulaResultPath>ciuFormula</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c &quot;_&quot; SUBSTRING 1 SUBSTRING</formulaConverted>
        <formulaExpression>SUBSTRING(SUBSTRING(AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c, &quot;_&quot;),1)</formulaExpression>
        <formulaResultPath>compagniaNumber</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetailsId</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem2</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem3</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c &apos;SOC_1&apos; == &quot;unipolsai&quot; &quot;unisalute&quot; IF</formulaConverted>
        <formulaExpression>IF(AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c  == &apos;SOC_1&apos;, &quot;unipolsai&quot;, &quot;unisalute&quot;)</formulaExpression>
        <formulaResultPath>compagnia</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem0</globalKey>
        <inputFieldName>FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Mandato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem10</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem8</globalKey>
        <inputFieldName>Agency__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:FiscalCode__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem9</globalKey>
        <inputFieldName>FiscalCode__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>AGE</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceCanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem16</globalKey>
        <inputFieldName>ciuFormula</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem13</globalKey>
        <inputFieldName>NetworkUser:NetworkUser__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>userId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom7553</globalKey>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem14</globalKey>
        <inputFieldName>compagniaNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>compagniaNumber</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:User:FirstName &quot;/\/\/&quot; var:User:LastName CONCAT</formulaConverted>
        <formulaExpression>CONCAT(User:FirstName, &quot; &quot; , User:LastName)</formulaExpression>
        <formulaResultPath>username</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>UniGetNetworkUserInfoCustom0jI9O000000trJpUAIItem12</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetNetworkUserInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Mandato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;UserId&quot; : &quot;0059X00000Jr08cQAB&quot;,
  &quot;AccountDetailsId&quot; : &quot;a1i9X000006lyenQAA&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetNetworkUserInfo_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
