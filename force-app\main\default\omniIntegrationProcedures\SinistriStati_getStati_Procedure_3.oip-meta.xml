<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;recordId&quot;: &quot;0019X000015e2ZLQAY&quot;,
    &quot;userId&quot;: &quot;0059X00000Jr08cQAB&quot;
}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>false</isActive>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>SinistriStati/getStati</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GenericLog</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;Sinistri&quot;,
    &quot;Payload&quot; : &quot;%GetAccount%&quot;,
    &quot;ClassName&quot; : &quot;SinistriStati&quot;,
    &quot;MethodName&quot; : &quot;GetAccountSinistri&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAccount</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;getAccountSinistri&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;recordId&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  }, {
    &quot;inputParam&quot; : &quot;User.UserId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>HTTPActionGetSinistriAperti</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;sinistriAperti&quot;,
  &quot;sendJSONPath&quot; : &quot;Request&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/contatore/aperti&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceFiscale&quot; : &quot;%GetAccount:accountDetail:codiceFiscale%&quot;,
      &quot;userId&quot; : &quot;%GetAccount:networkUser:userId%&quot;,
      &quot;codiceCompagnia&quot; : &quot;%GetAccount:codiceCompagnia%&quot;,
      &quot;codiceAgenzia&quot; : &quot;%GetAccount:Account:codiceAgenzia%&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>HTTPActionGetSinistriChiusi</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SinistriChiusi&quot;,
  &quot;sendJSONPath&quot; : &quot;Request&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/contatore/chiusi&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceFiscale&quot; : &quot;%GetAccount:accountDetail:codiceFiscale%&quot;,
      &quot;userId&quot; : &quot;%GetAccount:networkUser:userId%&quot;,
      &quot;codiceCompagnia&quot; : &quot;%GetAccount:codiceCompagnia%&quot;,
      &quot;codiceAgenzia&quot; : &quot;%GetAccount:Account:codiceAgenzia%&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Request</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;userId&quot; : &quot;=%GetAccount:networkUser:userId%&quot;,
    &quot;codiceCompagnia&quot; : &quot;=%GetAccount:codiceCompagnia%&quot;,
    &quot;codiceAgenzia&quot; : &quot;=%GetAccount:Account:codiceAgenzia%&quot;,
    &quot;codiceFiscale&quot; : &quot;=%GetAccount:accountDetail:codiceFiscale%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;response&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetValues</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;contatoreChiusi&quot; : &quot;=%SinistriChiusi%&quot;,
    &quot;contatoreAperti&quot; : &quot;=%sinistriAperti%&quot;,
    &quot;contatoreGenerale&quot; : &quot;=(%SinistriChiusi% + %sinistriAperti%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;response&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>SinistriStati_getStati</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>getStati</subType>
    <type>SinistriStati</type>
    <uniqueName>SinistriStati_getStati_Procedure_3</uniqueName>
    <versionNumber>3.0</versionNumber>
    <webComponentKey>e8ea5744-a240-0876-decb-3d700b6e599d</webComponentKey>
</OmniIntegrationProcedure>
