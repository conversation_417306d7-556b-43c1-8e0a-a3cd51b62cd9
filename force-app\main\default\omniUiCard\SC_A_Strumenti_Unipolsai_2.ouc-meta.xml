<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>SC_A_Prodotti/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Strumenti_GetPreferiti&quot;,&quot;vlocityAsync&quot;:false,&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000JqwMrQAJ&quot;,&quot;id&quot;:0}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>SC_A_Strumenti</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EStrumenti%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;},&quot;elementLabel&quot;:&quot;Text-1&quot;,&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F1F1F1&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#B0B0B0&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F1F1F1;     border-top: #B0B0B0 1px solid;border-right: #B0B0B0 1px solid;border-bottom: #B0B0B0 1px solid;border-left: #B0B0B0 1px solid; \n    border-radius:4px;     &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;standard:business_hours&quot;,&quot;size&quot;:&quot;small&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon-standard-business-hours &quot;,&quot;variant&quot;:&quot;inverse&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-m-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-0-Icon-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center slds-m-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_0_0_flexIcon_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERinnovi%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;},&quot;elementLabel&quot;:&quot;Block-0-Block-0-Text-1&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_0_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_0_0&quot;},{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;utility:new_window&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon__svg--default &quot;,&quot;variant&quot;:&quot;default&quot;,&quot;imgsrc&quot;:&quot;&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753105778605-6vswmrgib&quot;,&quot;label&quot;:&quot;Apri Rinnovi&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753105778821&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;SC_A_StrumentiModaleRinnovi&quot;,&quot;flyoutLwc&quot;:&quot;SC_A_StrumentiModaleRinnovi&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-m-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;elementLabel&quot;:&quot;Block-1-Icon-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-m-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_0_0_flexIcon_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F1F1F1&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#B0B0B0&quot;,&quot;radius&quot;:&quot;4px&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F1F1F1;     border-top: #B0B0B0 1px solid;border-right: #B0B0B0 1px solid;border-bottom: #B0B0B0 1px solid;border-left: #B0B0B0 1px solid; \n    border-radius:4px;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Campagne&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752755594286&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;customLwc&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;flyoutLwc&quot;:&quot;feiContainer&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;FEIID&quot;:&quot;CAMPAGNE&quot;}},&quot;key&quot;:&quot;1752755571600-ccpw4xyhm&quot;,&quot;label&quot;:&quot;FEI&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;elementLabel&quot;:&quot;Block-3-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_0_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_0_0&quot;},{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;utility:new_window&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon__svg--default &quot;,&quot;variant&quot;:&quot;default&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;elementLabel&quot;:&quot;Block-1-Icon-2-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_0_0_flexIcon_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_0_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_0_block_1_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;FastData&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752755602029&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;customLwc&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;flyoutLwc&quot;:&quot;feiContainer&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;FEIID&quot;:&quot;FAST.DATA&quot;}},&quot;key&quot;:&quot;1752755472968-5ovy7f7t5&quot;,&quot;label&quot;:&quot;FEI&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;utility:new_window&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon__svg--default &quot;,&quot;variant&quot;:&quot;default&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;elementLabel&quot;:&quot;Block-3-Block-2-Icon-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;&quot;,&quot;class&quot;:&quot;slds-text-align_left &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_1_0_flexIcon_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_1_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;SC_A_Strumenti_Preferiti&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{records}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-3-Block-3-FlexCard-0&quot;,&quot;key&quot;:&quot;element_element_block_2_0_childCardPreview_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_action_3_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Visualizza Tutto&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752154938304&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:false,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;Menu_Strumenti&quot;}},&quot;key&quot;:&quot;1752151945122-kasrszj0u&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:false}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;ariaLabel&quot;:&quot;Visualizza Tutto&quot;,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{},&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;Medium&quot;,&quot;fontSize&quot;:&quot;medium&quot;}}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;top:xx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;1&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;Medium&quot;,&quot;fontSize&quot;:&quot;medium&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-p-top_small slds-m-top_xx-small &quot;,&quot;style&quot;:&quot;     border-top: #cccccc 1px solid; \n    border-radius:1;     &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Action-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;top:xx-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;1&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;,&quot;textDecoration&quot;:&quot;Medium&quot;,&quot;fontSize&quot;:&quot;medium&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-p-top_small slds-m-top_xx-small &quot;,&quot;style&quot;:&quot;     border-top: #cccccc 1px solid; \n    border-radius:1;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-3&quot;}]}},&quot;childCards&quot;:[&quot;SC_A_Strumenti_Preferiti&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Strumenti_GetPreferiti&quot;,&quot;vlocityAsync&quot;:false,&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000JqwMrQAJ&quot;,&quot;id&quot;:0}]},&quot;title&quot;:&quot;SC_A_Strumenti&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSC_A_Strumenti_2_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003X7IjSAK&quot;,&quot;MasterLabel&quot;:&quot;cfSC_A_Strumenti_2_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPgogICAgICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPjx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19BcHBQYWdlIj4KICAgICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgICAgPHByb3BlcnR5IG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciLz4KICAgICAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;masterLabel&quot;:&quot;SC_A_Strumenti&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;sessionVars&quot;:[],&quot;requiredPermission&quot;:&quot;&quot;,&quot;isRepeatable&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;fiscalCode&quot;:null,&quot;apiName&quot;:&quot;UN_DISPOCANALDIGI&quot;,&quot;redirectType&quot;:&quot;standard__navItemPage&quot;,&quot;redirect&quot;:&quot;Disponibilita_Agenda&quot;,&quot;type&quot;:&quot;REDIRECT INTERNO&quot;,&quot;masterLabel&quot;:&quot;Disponibilità su Canali Digitali&quot;},{&quot;fiscalCode&quot;:null,&quot;apiName&quot;:&quot;UN_PREVAUTORIZZ&quot;,&quot;feiId&quot;:&quot;PREVENTIVI.AUTORIZZAZIONI&quot;,&quot;type&quot;:&quot;FEI&quot;,&quot;masterLabel&quot;:&quot;Preventivi e Autorizzazioni&quot;}]</sampleDataSourceResponse>
    <versionNumber>2</versionNumber>
</OmniUiCard>
