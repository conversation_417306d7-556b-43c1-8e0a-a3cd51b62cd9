<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>flyoutRow/Unipolsai/2.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;documentDetails\&quot;]&quot;,&quot;ipMethod&quot;:&quot;UniDS_Documents&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:1}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <name>UniDocumentazioneRow</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7Btipologia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;2&quot;,&quot;large&quot;:&quot;2&quot;,&quot;medium&quot;:&quot;2&quot;,&quot;small&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_right  slds-p-around_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BdataScadenza%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;label&quot;:&quot;left:medium&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;left:large&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-large&quot;,&quot;label&quot;:&quot;left:x-large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small slds-m-left_medium slds-m-left_large slds-m-left_x-large &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;label&quot;:&quot;left:medium&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;left:large&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-large&quot;,&quot;label&quot;:&quot;left:x-large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small slds-m-left_medium slds-m-left_large slds-m-left_x-large &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BnomeCompagnia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small slds-m-left_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center  slds-p-around_small slds-m-left_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%7BnumeroDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;},{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;left:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:100px;\nmin-width:70px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right  slds-p-around_x-small slds-m-bottom_x-small slds-m-left_x-small slds-m-top_xxx-small slds-m-left_large &quot;,&quot;style&quot;:&quot;      \n         max-width:100px;\nmin-width:70px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;3&quot;,&quot;medium&quot;:&quot;3&quot;,&quot;small&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_3-of-12 slds-medium-size_3-of-12 slds-small-size_3-of-12 slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;},{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;},{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;left:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;right&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:100px;\nmin-width:70px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_right  slds-p-around_x-small slds-m-bottom_x-small slds-m-left_x-small slds-m-top_xxx-small slds-m-left_large &quot;,&quot;style&quot;:&quot;      \n         max-width:100px;\nmin-width:70px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Menu&quot;,&quot;element&quot;:&quot;flexMenu&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;action&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;variant&quot;:&quot;&quot;,&quot;iconName&quot;:&quot;utility:down&quot;,&quot;overflow&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;menuItems&quot;:[{&quot;name&quot;:&quot;menu-item-1743177862039-0&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743177926683-o0yo2loow&quot;,&quot;label&quot;:&quot;Scarica Documento&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;test-action&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;}}}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-1743413158864-0&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413158877-5bsh0eczd&quot;,&quot;label&quot;:&quot;Storico Documento&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752761307033&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;flyoutLwc&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;tipologia&quot;:&quot;{tipologia}&quot;,&quot;dataScadenza&quot;:&quot;{dataScadenza}&quot;,&quot;nomeCompagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;numeroDocumento&quot;:&quot;{numeroDocumento}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconPosition&quot;:&quot;&quot;},{&quot;name&quot;:&quot;menu-item-1743413227700-0&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413227718-6w44vbt6n&quot;,&quot;label&quot;:&quot;Modifica&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752760178101&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;UniDoc/UpdateDocument/English&quot;,&quot;flyoutLwc&quot;:&quot;uni-doc-update-document-english&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{recordId}&quot;,&quot;dataScadenza&quot;:&quot;{dataScadenza}&quot;,&quot;tipologia&quot;:&quot;{tipologia}&quot;,&quot;nomeCompagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;provincia&quot;:&quot;{provincia}&quot;,&quot;numeroDocumento&quot;:&quot;{numeroDocumento}&quot;,&quot;externalId&quot;:&quot;{externalId}&quot;,&quot;comune&quot;:&quot;{comune}&quot;,&quot;stato&quot;:&quot;{stato}&quot;,&quot;dataValidata&quot;:&quot;{dataValidita}&quot;,&quot;tipologiaRT&quot;:&quot;{tipologiaRT}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},{&quot;name&quot;:&quot;menu-item-1743413256473-0&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;iconName&quot;:&quot;&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743413256485-tdc8igpp6&quot;,&quot;label&quot;:&quot;Elimina&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752757483617&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;UniDocumentale_Elimina_Flyout&quot;,&quot;flyoutLwc&quot;:&quot;UniDocumentale_Elimina_Flyout&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;externalId&quot;:&quot;{externalId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;}},&quot;actionIndex&quot;:0,&quot;reRenderFlyout&quot;:true,&quot;preloadFlyout&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}],&quot;position&quot;:&quot;right&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;},{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:71px;\nmin-width: 71px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_left slds-m-bottom_xx-small slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         max-width:71px;\nmin-width: 71px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;elementLabel&quot;:&quot;Menu-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-large-size_1-of-12 slds-medium-size_1-of-12 slds-small-size_1-of-12 slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;1&quot;,&quot;large&quot;:&quot;1&quot;,&quot;medium&quot;:&quot;1&quot;,&quot;small&quot;:&quot;1&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;},{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;left&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;max-width:71px;\nmin-width: 71px;\n&quot;,&quot;class&quot;:&quot;slds-theme_default slds-text-align_left slds-m-bottom_xx-small slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         max-width:71px;\nmin-width: 71px;\n&quot;,&quot;theme&quot;:&quot;theme_default&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_flexMenu_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;slds-border_bottom &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #e5e5e5 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;documentDetails\&quot;]&quot;,&quot;ipMethod&quot;:&quot;UniDS_Documents&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X000015e2ZLQAY&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;UniDocumentazioneRow&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003ASu9SAG&quot;,&quot;MasterLabel&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;documentDetails&quot;:[{&quot;dataScadenza&quot;:&quot;2025-12-15&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Roma&quot;,&quot;numeroDocumento&quot;:&quot;Documento 2&quot;,&quot;externalId&quot;:&quot;2342355&quot;,&quot;comune&quot;:&quot;Roma&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;dataValidita&quot;:&quot;2025-07-17&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;},{&quot;dataScadenza&quot;:&quot;2025-12-15&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;nomeCompagnia&quot;:&quot;unipolsai&quot;,&quot;ciu&quot;:&quot;1719&quot;,&quot;provincia&quot;:&quot;Firenze&quot;,&quot;numeroDocumento&quot;:&quot;Documento 1&quot;,&quot;externalId&quot;:&quot;333523525&quot;,&quot;comune&quot;:&quot;Firenze&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;dataValidita&quot;:&quot;2025-07-17&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;},{&quot;dataScadenza&quot;:&quot;2025-12-15&quot;,&quot;tipologia&quot;:&quot;CERTIFICATO DI NASCITA&quot;,&quot;nomeCompagnia&quot;:&quot;unisalute&quot;,&quot;ciu&quot;:&quot;42738858&quot;,&quot;provincia&quot;:&quot;Milano&quot;,&quot;numeroDocumento&quot;:&quot;Document 3&quot;,&quot;externalId&quot;:&quot;3425262&quot;,&quot;comune&quot;:&quot;Milano&quot;,&quot;stato&quot;:&quot;Italia&quot;,&quot;dataValidita&quot;:&quot;2025-07-17&quot;,&quot;tipologiaRT&quot;:&quot;CDN&quot;}]}</sampleDataSourceResponse>
    <versionNumber>14</versionNumber>
</OmniUiCard>
