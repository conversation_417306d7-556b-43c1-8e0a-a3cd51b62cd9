<?xml version="1.0" encoding="UTF-8"?>
<SharingRules xmlns="http://soap.sforce.com/2006/04/metadata">
    <sharingCriteriaRules>
        <fullName>Sharing_SOC1_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOC1- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_1</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_SOC2_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOC2- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_2</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_SOC4_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOC4- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_4</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_SOC5_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOC5- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_5</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_SOCS_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOCS- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_S</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_SOCV_With_ALL_SOC</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing SOCV- With ALL SOC</label>
        <sharedTo>
            <group>SOC_ALL</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Società</value>
        </criteriaItems>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_V</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Sharing_Soc_Rental_with_Rental_Group</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>None</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Sharing_Soc_Rental_with_Rental_Group</label>
        <sharedTo>
            <group>SOC_V</group>
        </sharedTo>
        <criteriaItems>
            <field>ExternalId__c</field>
            <operation>equals</operation>
            <value>SOC_V</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
</SharingRules>