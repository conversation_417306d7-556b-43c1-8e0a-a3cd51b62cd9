["{}", "{\"nodes\":{\"LOAD_DATASET6\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"url\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"name\":\"DomainUrl\",\"label\":\"DomainUrl\",\"type\":\"analyticsDataset\"}}},\"LOAD_DATASET5\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"Id\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"sourceObjectName\":\"Opportunity\",\"label\":\"Opportunity\",\"connectionName\":\"SFDC_LOCAL\",\"type\":\"connectedDataset\"}}},\"JOIN0\":{\"schema\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[]},\"fields\":[]},\"sources\":[\"FORMULA3\",\"FORMULA2\"],\"action\":\"join\",\"parameters\":{\"leftKeys\":[\"Join\"],\"rightQualifier\":\"OppDom\",\"rightKeys\":[\"Join\"],\"joinType\":\"LOOKUP\"}},\"OUTPUT0\":{\"sources\":[\"DROP_FIELDS1\"],\"action\":\"save\",\"parameters\":{\"measuresToCurrencies\":[],\"fields\":[],\"dataset\":{\"name\":\"DomainOpp\",\"label\":\"DomainOpp\",\"folderName\":\"Developments\",\"type\":\"analyticsDataset\"}}},\"DROP_FIELDS0\":{\"sources\":[\"FORMULA1\"],\"action\":\"schema\",\"parameters\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[\"url\"]},\"fields\":[]}},\"EDIT_ATTRIBUTES0\":{\"sources\":[\"JOIN0\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"OppDomain\",\"label\":\"OppDomain\"},\"name\":\"OppDom.DomainExtract\"}]}},\"FORMULA1\":{\"sources\":[\"LOAD_DATASET6\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"substr(url,1,(instr(url,'my',1,1)-1))\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"DomainExtract\",\"label\":\"Domain Extract\",\"type\":\"TEXT\"}]}},\"DROP_FIELDS1\":{\"sources\":[\"EDIT_ATTRIBUTES0\"],\"action\":\"schema\",\"parameters\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[\"Join\",\"OppDom.Join\"]},\"fields\":[]}},\"FORMULA2\":{\"sources\":[\"DROP_FIELDS0\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"'Test'\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"Join\",\"label\":\"Join\",\"type\":\"TEXT\"}]}},\"FORMULA3\":{\"sources\":[\"LOAD_DATASET5\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"'Test'\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"Join\",\"label\":\"Opportunity ID FormulaJoin\",\"type\":\"TEXT\"}]}}},\"ui\":{\"hiddenColumns\":[],\"connectors\":[{\"source\":\"TRANSFORM4\",\"target\":\"JOIN0\"},{\"source\":\"TRANSFORM2\",\"target\":\"JOIN0\"},{\"source\":\"JOIN0\",\"target\":\"TRANSFORM3\"},{\"source\":\"TRANSFORM3\",\"target\":\"OUTPUT0\"},{\"source\":\"LOAD_DATASET5\",\"target\":\"TRANSFORM4\"},{\"source\":\"LOAD_DATASET6\",\"target\":\"TRANSFORM1\"},{\"source\":\"TRANSFORM1\",\"target\":\"TRANSFORM2\"}],\"nodes\":{\"TRANSFORM4\":{\"connectors\":[],\"top\":252.5,\"left\":671.5,\"description\":\"\",\"label\":\"Transform Join\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA3\":{\"label\":\"Join\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"LOAD_DATASET6\":{\"top\":112,\"left\":112,\"label\":\"DomainUrl\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"LOAD_DATASET5\":{\"top\":392.5,\"left\":671.5,\"label\":\"Opportunity\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"JOIN0\":{\"top\":112.5,\"left\":671.5,\"description\":\"\",\"label\":\"Join OppDomain\",\"type\":\"JOIN\"},\"OUTPUT0\":{\"top\":112.4,\"left\":951.5,\"description\":\"\",\"label\":\"DomainOpp\",\"type\":\"OUTPUT\"},\"TRANSFORM1\":{\"connectors\":[],\"top\":112.4,\"left\":251.5,\"description\":\"\",\"label\":\"DomainExtract\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA1\":{\"label\":\"DomainExtract\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"TRANSFORM2\":{\"connectors\":[{\"source\":\"DROP_FIELDS0\",\"target\":\"FORMULA2\"}],\"top\":112.5,\"left\":531.5,\"description\":\"\",\"label\":\"Drop ExtraFields\",\"type\":\"TRANSFORM\",\"graph\":{\"DROP_FIELDS0\":{\"label\":\"Drop Columns\"},\"FORMULA2\":{\"label\":\"Join\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"TRANSFORM3\":{\"connectors\":[{\"source\":\"EDIT_ATTRIBUTES0\",\"target\":\"DROP_FIELDS1\"}],\"top\":112.4,\"left\":811.5,\"description\":\"\",\"label\":\"Transform Domain\",\"type\":\"TRANSFORM\",\"graph\":{\"EDIT_ATTRIBUTES0\":{\"label\":\"Edit Attributes\"},\"DROP_FIELDS1\":{\"label\":\"Drop Columns\"}}}}},\"version\":\"64.0\",\"runMode\":\"full\"}"]