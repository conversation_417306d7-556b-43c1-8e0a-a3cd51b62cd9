{"version": "64.0", "nodes": {"FORMULA1": {"action": "formula", "sources": ["LOAD_DATASET6"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "DomainExtract", "label": "Domain Extract", "formulaExpression": "substr(url,1,(instr(url,'my',1,1)-1))", "precision": 255, "defaultValue": ""}]}}, "DROP_FIELDS0": {"action": "schema", "sources": ["FORMULA1"], "parameters": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": ["url"]}}}, "FORMULA2": {"action": "formula", "sources": ["DROP_FIELDS0"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "Join", "label": "Join", "formulaExpression": "'Test'", "precision": 255, "defaultValue": ""}]}}, "LOAD_DATASET5": {"action": "load", "sources": [], "parameters": {"fields": ["Id"], "dataset": {"type": "connectedDataset", "label": "Opportunity", "connectionName": "SFDC_LOCAL", "sourceObjectName": "Opportunity"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "JOIN0": {"action": "join", "sources": ["FORMULA3", "FORMULA2"], "schema": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": []}}, "parameters": {"joinType": "LOOKUP", "leftKeys": ["Join"], "rightQualifier": "OppDom", "rightKeys": ["Join"]}}, "EDIT_ATTRIBUTES0": {"action": "schema", "sources": ["JOIN0"], "parameters": {"fields": [{"name": "OppDom.DomainExtract", "newProperties": {"label": "OppDomain", "name": "OppDomain"}}]}}, "DROP_FIELDS1": {"action": "schema", "sources": ["EDIT_ATTRIBUTES0"], "parameters": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": ["Join", "OppDom.Join"]}}}, "OUTPUT0": {"action": "save", "sources": ["DROP_FIELDS1"], "parameters": {"fields": [], "dataset": {"type": "analyticsDataset", "label": "DomainOpp", "name": "DomainOpp", "folderName": "Developments"}, "measuresToCurrencies": []}}, "FORMULA3": {"action": "formula", "sources": ["LOAD_DATASET5"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "Join", "label": "Opportunity ID FormulaJoin", "formulaExpression": "'Test'", "precision": 255, "defaultValue": ""}]}}, "LOAD_DATASET6": {"action": "load", "sources": [], "parameters": {"fields": ["url"], "dataset": {"type": "analyticsDataset", "label": "DomainUrl", "name": "DomainUrl"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}}, "ui": {"nodes": {"TRANSFORM1": {"label": "DomainExtract", "description": "", "type": "TRANSFORM", "top": 112.4, "left": 251.5, "graph": {"FORMULA1": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "DomainExtract"}}, "connectors": []}, "TRANSFORM2": {"label": "Drop ExtraFields", "description": "", "type": "TRANSFORM", "top": 112.5, "left": 531.5, "graph": {"DROP_FIELDS0": {"label": "Drop Columns"}, "FORMULA2": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "Join"}}, "connectors": [{"source": "DROP_FIELDS0", "target": "FORMULA2"}]}, "LOAD_DATASET5": {"label": "Opportunity", "type": "LOAD_DATASET", "top": 392.5, "left": 671.5, "parameters": {"sampleSize": 2000}}, "JOIN0": {"label": "Join <PERSON><PERSON>", "description": "", "type": "JOIN", "top": 112.5, "left": 671.5}, "TRANSFORM3": {"label": "Transform Domain", "description": "", "type": "TRANSFORM", "top": 112.4, "left": 811.5, "graph": {"EDIT_ATTRIBUTES0": {"label": "Edit Attributes"}, "DROP_FIELDS1": {"label": "Drop Columns"}}, "connectors": [{"source": "EDIT_ATTRIBUTES0", "target": "DROP_FIELDS1"}]}, "OUTPUT0": {"label": "DomainOpp", "description": "", "type": "OUTPUT", "top": 112.4, "left": 951.5}, "TRANSFORM4": {"label": "Transform Join", "description": "", "type": "TRANSFORM", "top": 252.5, "left": 671.5, "graph": {"FORMULA3": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "Join"}}, "connectors": []}, "LOAD_DATASET6": {"label": "DomainUrl", "type": "LOAD_DATASET", "top": 112, "left": 112, "parameters": {"sampleSize": 2000}}}, "connectors": [{"source": "TRANSFORM4", "target": "JOIN0"}, {"source": "TRANSFORM2", "target": "JOIN0"}, {"source": "JOIN0", "target": "TRANSFORM3"}, {"source": "TRANSFORM3", "target": "OUTPUT0"}, {"source": "LOAD_DATASET5", "target": "TRANSFORM4"}, {"source": "LOAD_DATASET6", "target": "TRANSFORM1"}, {"source": "TRANSFORM1", "target": "TRANSFORM2"}], "hiddenColumns": []}, "runMode": "full"}