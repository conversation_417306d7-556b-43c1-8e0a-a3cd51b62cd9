{"version": "64.0", "nodes": {"LOAD_DATASET0": {"action": "load", "sources": [], "parameters": {"fields": ["Probability", "ProductofInterest__c", "Product__c", "Id", "IsDeleted", "AccountId", "IsPrivate", "Name", "Description", "StageName", "Amount", "Expected<PERSON><PERSON><PERSON><PERSON>", "TotalOpportunityQuantity", "CloseDate", "Type", "NextStep", "LeadSource", "IsClosed", "IsWon", "ForecastCategory", "ForecastCategoryName", "HasOpportunityLineItem", "Pricebook2Id", "OwnerId", "CreatedDate", "LastModifiedDate", "SystemModstamp", "LastActivityDate", "FiscalQuarter", "FiscalYear", "Fiscal", "LastViewedDate", "LastReferencedDate", "HasOpenActivity", "HasOverdueTask", "AssignedTo__c", "AreaOfNeed__c", "AreasOfNeedFormula__c", "AccountNameFormula__c", "AssignedGroupName__c", "Assignee__c", "Channel__c", "ContactChannel__c", "CreationDate__c", "HasCallMeBack__c", "HasMultipleAreasFormula__c", "JourneyStep__c", "OwnerNameFormula__c", "Rating__c", "WorkingSLAExpiryDate__c", "Salespoint__c", "HasCallMeBackFormula__c", "NameAndChannel__c", "SalespointName__c", "TemperatureFormula__c", "AssignedGroup__c", "AssignmentDestination__c", "IsSetRef__c"], "dataset": {"type": "connectedDataset", "label": "Opportunity", "connectionName": "SFDC_LOCAL", "sourceObjectName": "Opportunity"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "JOIN0": {"action": "join", "sources": ["FILTER0", "LOAD_DATASET1"], "schema": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": []}}, "parameters": {"joinType": "LEFT_OUTER", "leftKeys": ["AccountId"], "rightQualifier": "Account<PERSON><PERSON><PERSON><PERSON>", "rightKeys": ["Id"]}}, "LOAD_DATASET1": {"action": "load", "sources": [], "parameters": {"fields": ["Id", "MasterRecordId", "Name", "Type", "RecordTypeId", "ParentId", "BillingStreet", "BillingCity", "BillingState", "BillingPostalCode", "BillingCountry", "BillingLatitude", "BillingLongitude", "BillingGeocodeAccuracy", "ShippingStreet", "ShippingCity", "ShippingState", "ShippingPostalCode", "ShippingCountry", "ShippingLatitude", "ShippingLongitude", "ShippingGeocodeAccuracy", "Phone", "Fax", "AccountNumber", "Website", "PhotoUrl", "Sic", "Industry", "AnnualRevenue", "NumberOfEmployees", "Ownership", "TickerSymbol", "Description", "Rating", "Site", "OwnerId", "CreatedDate", "LastModifiedDate", "SystemModstamp", "LastActivityDate", "LastViewedDate", "LastReferencedDate", "<PERSON><PERSON><PERSON>Account", "Jigsaw", "JigsawCompanyId", "AccountSource", "SicDesc", "FinServ__AUM__c", "FinServ__ClientCategory__c"], "dataset": {"type": "connectedDataset", "label": "Account", "connectionName": "SFDC_LOCAL", "sourceObjectName": "Account"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "JOIN1": {"action": "join", "sources": ["JOIN0", "LOAD_DATASET3"], "schema": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": []}}, "parameters": {"joinType": "LEFT_OUTER", "leftKeys": ["OwnerId"], "rightQualifier": "OpportunityOwner", "rightKeys": ["Id"]}}, "LOAD_DATASET3": {"action": "load", "sources": [], "parameters": {"fields": ["Id", "Username", "LastName", "FirstName", "Name", "CompanyName", "Division", "Department", "Title", "Street", "City", "State", "PostalCode", "Country", "Email", "Phone", "MobilePhone", "<PERSON><PERSON>", "IsActive", "TimeZoneSidKey", "UserRoleId", "ReceivesInfoEmails", "UserType", "ForecastEnabled", "FullPhotoUrl", "SmallPhotoUrl"], "dataset": {"type": "connectedDataset", "label": "User", "connectionName": "SFDC_LOCAL", "sourceObjectName": "User"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "FORMULA0": {"action": "formula", "sources": ["JOIN1"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "ReferenteFormula", "label": "<PERSON><PERSON><PERSON>", "formulaExpression": "\"OpportunityOwner.Name\"", "precision": 255, "defaultValue": ""}]}}, "FORMULA16": {"action": "formula", "sources": ["FORMULA0"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "Origine", "label": "Origine", "formulaExpression": "case upper(Channel__c )\r\nwhen 'AGENZ<PERSON>' then 'Altre'\r\nwhen 'CAMPAGNA' then 'Campagna' else Channel__c\r\nend", "precision": 255, "defaultValue": ""}]}}, "OUTPUT0": {"action": "save", "sources": ["FORMULA18"], "parameters": {"fields": [], "dataset": {"type": "analyticsDataset", "label": "Trattative Dataset", "name": "Trattative_Dataset", "rowLevelSecurityFilter": "'GroupSharing' == \"$User.Id\" || 'UserSharing' == \"$User.Id\" || 'SecuritySysAdm' == \"$User.ProfileId\"", "folderName": "Developments", "rowLevelSharingSource": "Opportunity"}, "measuresToCurrencies": []}}, "DROP_FIELDS1": {"action": "schema", "sources": ["FORMULA17"], "parameters": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": ["Image.OppDom.Id"]}}}, "FORMULA2": {"action": "formula", "sources": ["DROP_FIELDS1"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "RatingImage", "label": "Temperatura", "formulaExpression": "case \r\n\twhen \"Rating__c\" = 'Tiepida'\r\n\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/temp3warmpng')\r\n\r\n\twhen \"Rating__c\" = 'Calda'\r\n\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/temp2hotpng')\t\r\n\r\n\twhen \"Rating__c\" = 'Caldissima'\r\n\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/temp1veryhotpng')\r\n\r\n\telse\r\n    concat('https://',\"DomainExtract\",'file.force.com/file-asset/temp4coldpng')\r\n\t\r\nend", "precision": 255, "defaultValue": ""}]}}, "FORMULA3": {"action": "formula", "sources": ["FORMULA2"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "CallMeBackImage", "label": "Call Me Back", "formulaExpression": "case \r\n\twhen \"HasCallMeBack__c\" = 'true'\r\n\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/Completed_Iconpng')\t\r\n\r\n\telse\r\n    concat('https://',\"DomainExtract\",'file.force.com/file-asset/Expired_Iconpng')\r\n\t\r\nend", "precision": 255, "defaultValue": ""}]}}, "FORMULA5": {"action": "formula", "sources": ["FORMULA3"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "AmbitoImage", "label": "Ambito di protezione", "formulaExpression": "case when size(AreaOfNeed__c)<=1 then\r\n\tcase \r\n\t\twhen \"AreaOfNeedLinear\" = 'Cane e Gatto'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/petpng')\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Casa'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/casapng')\t\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Famiglia'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/famigliapng')\t\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Infortuni'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/infortunipng')\t\r\n\t\t\r\n\t\twhen \"AreaOfNeedLinear\" = 'Veicoli'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/veicolipng')\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Mobilità'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/mobilitapng')\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Salute'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/salutepng')\t\r\n\r\n\t\twhen \"AreaOfNeedLinear\" = 'Viaggio'\r\n\t\tthen concat('https://',\"DomainExtract\",'file.force.com/file-asset/viaggipng')\t\r\n\r\n\t\telse\r\n\t\tconcat('https://',\"DomainExtract\",'file.force.com/file-asset/emptypng')\t\r\n\t\t\r\n\tend\r\n\t\r\nelse \r\n\tcase when \"AreaOfNeedLinear\" in ('Casa', 'Famiglia', 'Cane e Gatto', 'Viaggio', 'Viaggi', 'Veicoli', 'Mobilità', 'Infortuni', 'Salute') \r\n\tthen\r\n\tconcat('https://',\"DomainExtract\",'file.force.com/file-asset/multicompartopng')\t\r\n\r\n\telse\r\n\tconcat('https://',\"DomainExtract\",'file.force.com/file-asset/multiambitopng')\r\nend\r\nend", "precision": 255, "defaultValue": ""}]}}, "FORMULA6": {"action": "formula", "sources": ["FORMULA5"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "AmbitoFilter", "label": "Ambito", "formulaExpression": "case when size(AreaOfNeed__c)<=1 then\r\n case \r\n\twhen \"AreaOfNeedLinear\" in ('Casa', 'Famiglia', 'Cane e Gatto', 'Viaggio', 'Viaggi')\r\n\tthen 'Unica - Casa e Famiglia'\r\n\twhen \"AreaOfNeedLinear\" in ('Veicoli', 'Mobilità')\r\n\tthen 'Unica - Veicoli e Mobilità'\r\n\twhen \"AreaOfNeedLinear\" in ('Infortuni', 'Salute')\r\n\tthen 'Unica - Persona'\r\n\telse\r\n        'Nessuno'\r\nend\r\nelse\r\n\tcase \r\n\t\twhen ( \r\n\t\tcontains(AreaOfNeedLinear,'Casa') or \r\n\t\tcontains(AreaOfNeedLinear,'Famiglia') or\r\n\t\tcontains(AreaOfNeedLinear, 'Cane e Gatto') or\r\n\t\tcontains(AreaOfNeedLinear, 'Viaggi')or\r\n\t\tcontains(AreaOfNeedLinear, 'Veicoli')or\r\n\t\tcontains(AreaOfNeedLinear, 'Mobilità')or\r\n\t\tcontains(AreaOfNeedLinear, 'Infortuni')or\r\n\t\tcontains(AreaOfNeedLinear, 'Salute')\r\n\t\t)\r\n\t\tthen 'Unica - Multiambito'\r\n\t\telse\r\n\t\t'Multicomparto'\r\n\t\tend\r\nend", "precision": 255, "defaultValue": ""}]}}, "FORMULA4": {"action": "formula", "sources": ["JOIN7"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "AreaOfNeedLinear", "label": "AreaOfNeedLinear", "formulaExpression": "array_join(AreaOfNeed__c, ',')", "precision": 255, "defaultValue": ""}]}}, "FORMULA17": {"action": "formula", "sources": ["FORMULA4"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "DomainExtract", "label": "DomainExtract", "formulaExpression": "\"DomainOpp.OppDomain\"", "precision": 255, "defaultValue": ""}]}}, "EDIT_ATTRIBUTES0": {"action": "schema", "sources": ["FORMULA10"], "parameters": {"fields": [{"name": "CloseDate", "newProperties": {"label": "<PERSON>", "name": "CloseDate", "typeProperties": {"type": "DATETIME", "format": "yyyy-MM-dd"}}}]}}, "EDIT_ATTRIBUTES1": {"action": "schema", "sources": ["EDIT_ATTRIBUTES2"], "parameters": {"fields": [{"name": "WorkingSLAExpiryDate__c", "newProperties": {"label": "Scadenza", "name": "WorkingSLAExpiryDate__c", "typeProperties": {"type": "DATETIME", "format": "yyyy-MM-dd"}}}]}}, "EDIT_ATTRIBUTES2": {"action": "schema", "sources": ["EDIT_ATTRIBUTES0"], "parameters": {"fields": [{"name": "CreatedDate", "newProperties": {"label": "Data Creazione", "name": "CreatedDate", "typeProperties": {"type": "DATETIME", "format": "yyyy-MM-dd"}}}]}}, "EDIT_ATTRIBUTES3": {"action": "schema", "sources": ["EDIT_ATTRIBUTES12"], "parameters": {"fields": [{"name": "StageName", "newProperties": {"label": "Stato", "name": "StageName"}}]}}, "EDIT_ATTRIBUTES4": {"action": "schema", "sources": ["EDIT_ATTRIBUTES3"], "parameters": {"fields": [{"name": "Amount", "newProperties": {"label": "Premio", "name": "Amount"}}]}}, "EDIT_ATTRIBUTES5": {"action": "schema", "sources": ["EDIT_ATTRIBUTES4"], "parameters": {"fields": [{"name": "Channel__c", "newProperties": {"label": "Canale Origine", "name": "Channel__c"}}]}}, "EDIT_ATTRIBUTES6": {"action": "schema", "sources": ["EDIT_ATTRIBUTES5"], "parameters": {"fields": [{"name": "JourneyStep__c", "newProperties": {"label": "Journey Step", "name": "JourneyStep__c"}}]}}, "EDIT_ATTRIBUTES7": {"action": "schema", "sources": ["EDIT_ATTRIBUTES6"], "parameters": {"fields": [{"name": "AssignedTo__c", "newProperties": {"label": "Utente Assegnatario", "name": "AssignedTo__c"}}]}}, "EDIT_ATTRIBUTES8": {"action": "schema", "sources": ["EDIT_ATTRIBUTES7"], "parameters": {"fields": [{"name": "Name", "newProperties": {"label": "Nome Trattativa", "name": "Name"}}]}}, "EDIT_ATTRIBUTES9": {"action": "schema", "sources": ["EDIT_ATTRIBUTES8"], "parameters": {"fields": [{"name": "AccountNameFormula__c", "newProperties": {"label": "<PERSON><PERSON><PERSON>", "name": "AccountNameFormula__c"}}]}}, "EDIT_ATTRIBUTES10": {"action": "schema", "sources": ["EDIT_ATTRIBUTES9"], "parameters": {"fields": [{"name": "ContactChannel__c", "newProperties": {"label": "Canale di contatto", "name": "ContactChannel__c"}}]}}, "EDIT_ATTRIBUTES11": {"action": "schema", "sources": ["EDIT_ATTRIBUTES10"], "parameters": {"fields": [{"name": "Product__c", "newProperties": {"label": "<PERSON><PERSON><PERSON>", "name": "Product__c"}}]}}, "EDIT_ATTRIBUTES12": {"action": "schema", "sources": ["EDIT_ATTRIBUTES1"], "parameters": {"fields": [{"name": "SalespointName__c", "newProperties": {"label": "<PERSON><PERSON><PERSON>", "name": "SalespointName__c"}}]}}, "FORMULA19": {"action": "formula", "sources": ["EDIT_ATTRIBUTES11"], "parameters": {"expressionType": "SQL", "fields": [{"type": "DATETIME", "name": "Data_Creazione_New", "label": "Data Creazione (New)", "formulaExpression": "CreatedDate", "format": "dd-MM-yyyy", "defaultValue": ""}]}}, "FORMULA10": {"action": "formula", "sources": ["FORMULA16"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "AssegnatarioName", "label": "Utente Assegnatario", "formulaExpression": "coalesce(Assignee__c,AssignedGroupName__c)", "precision": 255, "defaultValue": ""}]}}, "FORMULA11": {"action": "formula", "sources": ["JOIN6"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "SecuritySysAdm", "label": "SecuritySysAdm", "formulaExpression": "'00e7Q000004Nir1QAC'", "precision": 255, "defaultValue": ""}]}}, "FORMULA14": {"action": "formula", "sources": ["FORMULA11"], "parameters": {"expressionType": "SQL", "fields": [{"type": "TEXT", "name": "SecurityOperatori", "label": "SecurityOperatori", "formulaExpression": "'00e9X000007FpHWQA0'", "precision": 255, "defaultValue": ""}]}}, "FORMULA15": {"action": "formula", "sources": ["FORMULA14"], "parameters": {"expressionType": "SQL", "fields": [{"type": "MULTIVALUE", "name": "GroupSharing", "label": "GroupSharing", "formulaExpression": "\"OppSharing.GroupSharing\""}]}}, "FORMULA18": {"action": "formula", "sources": ["FORMULA15"], "parameters": {"expressionType": "SQL", "fields": [{"type": "MULTIVALUE", "name": "UserSharing", "label": "UserSharing", "formulaExpression": "\"OppSharing.UserSharing\""}]}}, "LOAD_DATASET8": {"action": "load", "sources": [], "parameters": {"fields": ["UserSharing", "GroupSharing", "Id"], "dataset": {"type": "analyticsDataset", "label": "OppWithUserAndGroupSharing", "name": "OppWithUserAndGroupSharing"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "JOIN6": {"action": "join", "sources": ["FORMULA6", "LOAD_DATASET8"], "schema": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": []}}, "parameters": {"joinType": "LOOKUP", "leftKeys": ["Id"], "rightQualifier": "OppSharing", "rightKeys": ["Id"]}}, "FILTER0": {"action": "filter", "sources": ["LOAD_DATASET0"], "parameters": {"filterExpressions": [{"type": "TEXT", "field": "Name", "operator": "DOES_NOT_CONTAIN", "operands": ["PR"]}]}}, "LOAD_DATASET9": {"action": "load", "sources": [], "parameters": {"fields": ["OppDomain", "Id"], "dataset": {"type": "analyticsDataset", "label": "DomainOpp", "name": "DomainOpp"}, "sampleDetails": {"type": "TopN", "sortBy": []}}}, "JOIN7": {"action": "join", "sources": ["FORMULA19", "LOAD_DATASET9"], "schema": {"fields": [], "slice": {"mode": "DROP", "ignoreMissingFields": true, "fields": []}}, "parameters": {"joinType": "LOOKUP", "leftKeys": ["Id"], "rightQualifier": "DomainOpp", "rightKeys": ["OppDomain"]}}}, "ui": {"nodes": {"LOAD_DATASET0": {"label": "Opportunity", "type": "LOAD_DATASET", "top": 112, "left": 112, "parameters": {"sampleSize": 2000}}, "JOIN0": {"label": "Join 0", "type": "JOIN", "top": 112, "left": 392}, "LOAD_DATASET1": {"label": "Account", "type": "LOAD_DATASET", "top": 252, "left": 392, "parameters": {"sampleSize": 2000}}, "JOIN1": {"label": "Opp vs User", "description": "", "type": "JOIN", "top": 112, "left": 532}, "LOAD_DATASET3": {"label": "User", "type": "LOAD_DATASET", "top": 252.2, "left": 532, "parameters": {"sampleSize": 2000}}, "TRANSFORM0": {"label": "Transform Fields", "description": "Per aggiunta formule", "type": "TRANSFORM", "top": 112, "left": 672, "graph": {"FORMULA0": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "<PERSON><PERSON><PERSON>"}, "FORMULA16": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "Origine"}}, "connectors": [{"source": "FORMULA0", "target": "FORMULA16"}]}, "OUTPUT0": {"label": "Trattative_Dataset", "description": "", "type": "OUTPUT", "top": 112, "left": 1792.2}, "TRANSFORM3": {"label": "Transform Image", "description": "", "type": "TRANSFORM", "top": 112, "left": 1372.2, "graph": {"DROP_FIELDS1": {"label": "Drop OppImageID"}, "FORMULA2": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "RatingImage"}, "FORMULA3": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "CallMeBackImage"}, "FORMULA5": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "AmbitoImage"}, "FORMULA6": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "AmbitoFilter"}}, "connectors": [{"source": "DROP_FIELDS1", "target": "FORMULA2"}, {"source": "FORMULA2", "target": "FORMULA3"}, {"source": "FORMULA3", "target": "FORMULA5"}, {"source": "FORMULA5", "target": "FORMULA6"}]}, "TRANSFORM4": {"label": "Transform AmbitoDom", "description": "", "type": "TRANSFORM", "top": 112, "left": 1232.2, "graph": {"FORMULA4": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "AmbitoFormula"}, "FORMULA17": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "DomainExtract"}}, "connectors": [{"source": "FORMULA4", "target": "FORMULA17"}]}, "TRANSFORM5": {"label": "Date Formatting", "description": "", "type": "TRANSFORM", "top": 112.1, "left": 952, "graph": {"EDIT_ATTRIBUTES0": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES1": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES2": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES3": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES4": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES5": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES6": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES7": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES8": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES9": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES10": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES11": {"label": "Edit Attributes"}, "EDIT_ATTRIBUTES12": {"label": "Edit Attributes"}, "FORMULA19": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "set Data Creazione dd-MM-YYYY"}}, "connectors": [{"source": "EDIT_ATTRIBUTES2", "target": "EDIT_ATTRIBUTES1"}, {"source": "EDIT_ATTRIBUTES0", "target": "EDIT_ATTRIBUTES2"}, {"source": "EDIT_ATTRIBUTES3", "target": "EDIT_ATTRIBUTES4"}, {"source": "EDIT_ATTRIBUTES4", "target": "EDIT_ATTRIBUTES5"}, {"source": "EDIT_ATTRIBUTES5", "target": "EDIT_ATTRIBUTES6"}, {"source": "EDIT_ATTRIBUTES6", "target": "EDIT_ATTRIBUTES7"}, {"source": "EDIT_ATTRIBUTES7", "target": "EDIT_ATTRIBUTES8"}, {"source": "EDIT_ATTRIBUTES8", "target": "EDIT_ATTRIBUTES9"}, {"source": "EDIT_ATTRIBUTES9", "target": "EDIT_ATTRIBUTES10"}, {"source": "EDIT_ATTRIBUTES10", "target": "EDIT_ATTRIBUTES11"}, {"source": "EDIT_ATTRIBUTES12", "target": "EDIT_ATTRIBUTES3"}, {"source": "EDIT_ATTRIBUTES1", "target": "EDIT_ATTRIBUTES12"}, {"source": "EDIT_ATTRIBUTES11", "target": "FORMULA19"}]}, "TRANSFORM6": {"label": "Transform Assegnatario Name", "description": "", "type": "TRANSFORM", "top": 112.1, "left": 812, "graph": {"FORMULA10": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "AssegnatarioNome"}}, "connectors": []}, "TRANSFORM7": {"label": "Transform Security", "description": "", "type": "TRANSFORM", "top": 112, "left": 1652.2, "graph": {"FORMULA11": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "SecuritySysAdm"}, "FORMULA14": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "SecurityOperatori"}, "FORMULA15": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "GroupSharing"}, "FORMULA18": {"parameters": {"type": "BASE_FORMULA_UI"}, "label": "UserSharing"}}, "connectors": [{"source": "FORMULA11", "target": "FORMULA14"}, {"source": "FORMULA14", "target": "FORMULA15"}, {"source": "FORMULA15", "target": "FORMULA18"}]}, "LOAD_DATASET8": {"label": "OppWithUserAndGroupSharing", "type": "LOAD_DATASET", "top": 251.89999999999998, "left": 1372, "parameters": {"sampleSize": 2000}}, "JOIN6": {"label": "Join <PERSON>", "description": "", "type": "JOIN", "top": 112, "left": 1512.2}, "FILTER0": {"label": "Filter Product", "description": "", "type": "FILTER", "top": 112, "left": 252}, "LOAD_DATASET9": {"label": "DomainOpp", "type": "LOAD_DATASET", "top": 251.3, "left": 1091.9, "parameters": {"sampleSize": 2000}}, "JOIN7": {"label": "Join Domain", "description": "", "type": "JOIN", "top": 112.1, "left": 1092}}, "connectors": [{"source": "FILTER0", "target": "JOIN0"}, {"source": "LOAD_DATASET1", "target": "JOIN0"}, {"source": "JOIN0", "target": "JOIN1"}, {"source": "LOAD_DATASET3", "target": "JOIN1"}, {"source": "JOIN1", "target": "TRANSFORM0"}, {"source": "TRANSFORM7", "target": "OUTPUT0"}, {"source": "TRANSFORM4", "target": "TRANSFORM3"}, {"source": "TRANSFORM6", "target": "TRANSFORM5"}, {"source": "JOIN6", "target": "TRANSFORM7"}, {"source": "TRANSFORM3", "target": "JOIN6"}, {"source": "LOAD_DATASET0", "target": "FILTER0"}, {"source": "LOAD_DATASET8", "target": "JOIN6"}, {"source": "TRANSFORM0", "target": "TRANSFORM6"}, {"source": "JOIN7", "target": "TRANSFORM4"}, {"source": "TRANSFORM5", "target": "JOIN7"}, {"source": "LOAD_DATASET9", "target": "JOIN7"}], "hiddenColumns": []}, "runMode": "full"}