<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_case_fields</name>
        <label>Assign case fields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Trasferito</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UtAssegnatario__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_owner</name>
        <label>assign owner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Copy_1_of_Tutti_gli_uffici</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_assign_owner</name>
        <label>assign owner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Tutti_gli_uffici</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_assign_owner</name>
        <label>assign owner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getQueueId.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_accountDetails</name>
        <label>Check accountDetails</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Ufficio_senza_suggeriti</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>if_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAccountDetails</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getUfficiSuggeriti</targetReference>
            </connector>
            <label>if exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_assegnatario</name>
        <label>Check assegnatario</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsg</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.UtAssegnatario__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>currentUserId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Screen_conferma</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckSelUfficio</name>
        <label>CheckSelUfficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_assign_owner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Uffici_Suggeriti_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Uffici_suggeriti</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getQueueId</targetReference>
            </connector>
            <label>Uffici Suggeriti not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>esistono_config_suggeriti</name>
        <label>esistono config suggeriti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Screen_Ufficio_senza_suggeriti</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_4</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getUffici</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Selezione_Ufficio</targetReference>
            </connector>
            <label>si</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>UfficiList</name>
        <collectionReference>getUffici</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>Group</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>UfficiSuggeritiList</name>
        <collectionReference>getUfficiSuggeriti</collectionReference>
        <dataType>String</dataType>
        <displayField>Ufficio__c</displayField>
        <object>urcs_ConfigUfficiSuggeriti__mdt</object>
        <valueField>QueueDevName__c</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>currentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <interviewLabel>u {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseTrasferimento</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>getAccountAccountRel</name>
        <label>getAccountAccountRel</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountDetails</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountSocRental.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountDetails</name>
        <label>getAccountDetails</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_accountDetails</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Relation__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountAccountRel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountDetails__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountSocRental</name>
        <label>getAccountSocRental</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountAccountRel</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.urcs_GeneralSettings__c.GroupSocRentalName__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_assegnatario</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getQueueId</name>
        <label>getQueueId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_2_of_assign_owner</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Uffici_suggeriti</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUffici</name>
        <label>getUffici</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountSocRental</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>urcs_</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUfficiSuggeriti</name>
        <label>getUfficiSuggeriti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>esistono_config_suggeriti</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Categoria__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Categoria__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>SottoCategoria__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.SottoCategoria__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Top__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountDetails.Top__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>EntChiamante__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.EntChiamante__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Ufficio__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>QueueDevName__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>urcs_ConfigUfficiSuggeriti__mdt</object>
        <sortField>QueueDevName__c</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <targetReference>ScreenErrorMsgOnUpdate</targetReference>
        </faultConnector>
        <inputReference>caseRecord</inputReference>
    </recordUpdates>
    <screens>
        <name>Screen_conferma</name>
        <label>Screen conferma</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getUffici</targetReference>
        </connector>
        <fields>
            <name>Text_conferma</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Sei sicuro di voler procedere con tale operazione?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_Ufficio_senza_suggeriti</name>
        <label>Screen Ufficio senza suggeriti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>assign_owner</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_Tutti_gli_uffici</name>
            <choiceReferences>UfficiList</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Tutti gli uffici</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenErrorMsg</name>
        <label>ScreenErrorMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Per effettuare l&apos;operazione è necessario prendere in carico il case.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenErrorMsgOnUpdate</name>
        <label>ScreenErrorMsgOnUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsgOnUpdate</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Si è verificato un errore: impossibile completare l&apos;operazione.&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Selezione_Ufficio</name>
        <label>Screen Ufficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>CheckSelUfficio</targetReference>
        </connector>
        <fields>
            <name>Uffici_suggeriti</name>
            <choiceReferences>UfficiSuggeritiList</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Uffici suggeriti</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
        </fields>
        <fields>
            <name>Tutti_gli_uffici</name>
            <choiceReferences>UfficiList</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Tutti gli uffici</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <styleProperties>
                <verticalAlignment>
                    <stringValue>top</stringValue>
                </verticalAlignment>
                <width>
                    <stringValue>12</stringValue>
                </width>
            </styleProperties>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Uffici_suggeriti</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>caseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
