------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------

THIS MANUAL PROCEDURE IS REQUIRED TO BE EXECUTED IN POST DEPLOYMENT.
The purpose of this manual procedure is to Assign the Group Member User to the SOC_ALL group that represents 
the group of all users of all societies in the system.

-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Click on the settings icon at the top right and go to the setup item
3. Click in the "Quick Find" of the tab "Home" on the left
   and write "Group"
4. Click on "Groups" in the results
5. Search the group namend "SOC_ALL"
5. Click on the edit link of the record "SOC_ALL" group
6. In the search list select "Public Group"
7. In the "for" input box insert "SOC"
8. Now in the "Available Members" section the "value":
    "SOC_1", "SOC_2", "SOC_4", "SOC_5", "SOC_V", "SOC_S" 
9. Click on the "Add" button
10. Click the "Save" button
