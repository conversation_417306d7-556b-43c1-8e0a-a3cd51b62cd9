------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to delete records
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Open Data export from saelsforce inspector
3. Paste the query below:


        SELECT id
        FROM Group 
        WHERE Type='Regular' 
        AND DeveloperName IN (
            'urcs_L1_Call2NetSupportoAreaRiservata',
            'urcs_L1_CallCenter',
            'urcs_L1_PostSalesAgenzie',
            'urcs_L1_UfficioAcceptance',
            'urcs_L2_ComplaintsOffice',
            'urcs_L2_CustomerCareSocial',
            'urcs_L2_FilialeUR',
            'urcs_L3_Amministrazione_FattLocazioni',
            'urcs_L3_Amministrazione_GestCarburante',
            'urcs_L3_Amministrazione_UffContravvenzio',
            'urcs_L3_BOFinance',
            'urcs_L3_UfficioAcquisti_Acquisti',
            'urcs_L3_UfficioAcquisti_Ordini',
            'urcs_L3_UfficioAcquisti_Stock'
        )

4.click run export and then copy (Excel)
5.Open Data import from saelsforce inspector
    API Type -> default
    Action -> Delete
    Object -> Group
    Data -> Paste the query response

6. Run Delete


7. Open Data export from saelsforce inspector
8. Paste the query below:


        SELECT id
        FROM Group 
        WHERE Type='Queue' 
        AND DeveloperName IN (
            'urcs_L1_Call2NetSupportoAreaRiservata',
            'urcs_L1_CallCenter',
            'urcs_L1_PostSalesAgenzie',
            'urcs_L1_UfficioAcceptance',
            'urcs_L2_ComplaintsOffice',
            'urcs_L2_CustomerCareSocial',
            'urcs_L2_FilialeUR',
            'urcs_L3_Amministrazione_FattLocazioni',
            'urcs_L3_Amministrazione_GestCarburante',
            'urcs_L3_Amministrazione_UffContravvenzio',
            'urcs_L3_BOFinance',
            'urcs_L3_UfficioAcquisti_Acquisti',
            'urcs_L3_UfficioAcquisti_Ordini',
            'urcs_L3_UfficioAcquisti_Stock'
        )

9.click run export and then copy (Excel)
10.Open Data import from saelsforce inspector
    API Type -> default
    Action -> Delete
    Object -> Group
    Data -> Paste the query response

11. Run Delete